"""
多轮Agent论文阅读系统
支持多个AI Agent协作进行论文深度分析
"""

import json
import requests
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum


class AgentRole(Enum):
    """Agent角色枚举"""
    ANALYST = "analyst"          # 分析师 - 论文初步分析
    SUMMARIZER = "summarizer"    # 总结师 - 内容总结
    CRITIC = "critic"           # 评论员 - 批判性分析  
    COORDINATOR = "coordinator"  # 协调员 - 整合协调


@dataclass
class AgentMessage:
    """Agent消息数据结构"""
    agent_role: AgentRole
    content: str
    timestamp: datetime
    round_id: int
    paper_id: int


@dataclass
class ReadingSession:
    """阅读会话数据结构"""
    session_id: str
    paper_id: int
    current_round: int
    messages: List[AgentMessage]
    analysis_result: Dict
    created_at: datetime


class MultiAgentReader:
    """多Agent论文阅读系统"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.llm_config = config.get('llm', {})
        self.sessions = {}  # 存储活跃的阅读会话
        
        # Agent系统提示词
        self.agent_prompts = {
            AgentRole.ANALYST: """你是一名专业的论文分析师AI Agent。你的任务是：

1. 快速理解论文的核心问题和研究背景
2. 识别论文的主要贡献和创新点
3. 分析论文的方法论和技术路线
4. 评估论文的实验设计和数据质量

请用简洁专业的语言进行分析，重点关注：
- 研究问题的明确性和重要性
- 方法的新颖性和有效性
- 实验的完整性和说服力

输出格式为结构化的分析报告。""",

            AgentRole.SUMMARIZER: """你是一名专业的论文总结师AI Agent。你的任务是：

1. 提取论文的关键信息和要点
2. 总结论文的主要内容和结论
3. 整理论文的逻辑结构和论述脉络
4. 生成易于理解的摘要和总结

请用清晰简洁的语言进行总结，确保：
- 准确传达论文的核心内容
- 保持逻辑结构的完整性
- 突出重要的发现和结论

输出格式为层次化的内容总结。""",

            AgentRole.CRITIC: """你是一名专业的论文评论员AI Agent。你的任务是：

1. 批判性分析论文的方法和结论
2. 识别论文可能存在的问题和局限性
3. 评估论文的逻辑严密性和证据充分性
4. 提出改进建议和进一步研究方向

请保持客观公正的评判态度，重点关注：
- 方法论的严谨性
- 结论的可靠性和普适性
- 实验的局限性
- 潜在的研究漏洞

输出格式为批判性分析报告。""",

            AgentRole.COORDINATOR: """你是一名专业的论文阅读协调员AI Agent。你的任务是：

1. 综合各专家Agent的分析结果
2. 协调不同观点和发现之间的关系
3. 生成完整的论文理解报告
4. 为后续深入研究提供指导建议

请整合所有Agent的分析，确保：
- 信息的完整性和一致性
- 观点的平衡性和客观性
- 结论的准确性和实用性

输出格式为综合性的论文解读报告。"""
        }
    
    def create_reading_session(self, paper_id: int, paper_content: str, paper_metadata: Dict) -> str:
        """创建新的论文阅读会话"""
        session_id = f"session_{paper_id}_{int(datetime.now().timestamp())}"
        
        session = ReadingSession(
            session_id=session_id,
            paper_id=paper_id,
            current_round=0,
            messages=[],
            analysis_result={},
            created_at=datetime.now()
        )
        
        self.sessions[session_id] = session
        return session_id
    
    def call_agent_api(self, agent_role: AgentRole, user_message: str, context: str = "") -> str:
        """调用单个Agent的API"""
        try:
            # 构建消息
            messages = [
                {"role": "system", "content": self.agent_prompts[agent_role]},
                {"role": "user", "content": f"论文内容：\n{context}\n\n用户问题：{user_message}"}
            ]
            
            headers = {
                'Authorization': f'Bearer {self.llm_config.get("api_key", "")}',
                'Content-Type': 'application/json',
                'HTTP-Referer': 'http://localhost:5001',
                'X-Title': 'DocuMancer-MultiAgent'
            }

            payload = {
                'model': self.llm_config.get('model', 'google/gemini-2.5-flash'),
                'messages': messages,
                'max_tokens': self.llm_config.get('max_tokens', 2048),
                'temperature': self.llm_config.get('temperature', 0.7),
                'stream': False
            }

            print(f"调用{agent_role.value} Agent API...")
            
            response = requests.post(
                f"{self.llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content']
                else:
                    return f"{agent_role.value} Agent暂时无法回复"
            else:
                print(f"{agent_role.value} Agent API调用失败: {response.status_code}")
                return f"{agent_role.value} Agent服务暂时不可用"

        except Exception as e:
            print(f"{agent_role.value} Agent API调用错误: {e}")
            return f"{agent_role.value} Agent遇到技术问题"
    
    def run_multi_agent_analysis(self, session_id: str, paper_content: str, paper_metadata: Dict, user_query: str = "") -> Dict:
        """运行多Agent协作分析"""
        if session_id not in self.sessions:
            raise ValueError("会话不存在")
        
        session = self.sessions[session_id]
        session.current_round += 1
        
        # 构建论文上下文
        context = f"""
标题: {paper_metadata.get('title', '未知')}
作者: {paper_metadata.get('authors', '未知')}
摘要: {paper_metadata.get('abstract', '无摘要')}

论文内容:
{paper_content[:8000]}  # 限制长度避免token超限
"""
        
        # 用户查询或默认分析任务
        if not user_query.strip():
            user_query = "请对这篇论文进行全面深入的分析，包括其研究问题、方法、贡献、实验结果和潜在问题。"
        
        results = {}
        
        # 第一轮：分析师Agent进行初步分析
        print("🔍 分析师Agent开始分析...")
        analyst_result = self.call_agent_api(AgentRole.ANALYST, user_query, context)
        results['analyst'] = analyst_result
        
        # 记录消息
        session.messages.append(AgentMessage(
            agent_role=AgentRole.ANALYST,
            content=analyst_result,
            timestamp=datetime.now(),
            round_id=session.current_round,
            paper_id=session.paper_id
        ))
        
        # 第二轮：总结师Agent基于分析师结果进行总结
        print("📝 总结师Agent开始工作...")
        summarizer_context = f"{context}\n\n分析师的分析结果：\n{analyst_result}"
        summarizer_result = self.call_agent_api(AgentRole.SUMMARIZER, 
                                               "基于分析师的分析，请对论文进行结构化总结", 
                                               summarizer_context)
        results['summarizer'] = summarizer_result
        
        session.messages.append(AgentMessage(
            agent_role=AgentRole.SUMMARIZER,
            content=summarizer_result,
            timestamp=datetime.now(),
            round_id=session.current_round,
            paper_id=session.paper_id
        ))
        
        # 第三轮：评论员Agent进行批判性分析
        print("🤔 评论员Agent开始批判性分析...")
        critic_context = f"{context}\n\n分析师结果：\n{analyst_result}\n\n总结师结果：\n{summarizer_result}"
        critic_result = self.call_agent_api(AgentRole.CRITIC, 
                                          "请对论文进行批判性分析，指出可能的问题和局限性", 
                                          critic_context)
        results['critic'] = critic_result
        
        session.messages.append(AgentMessage(
            agent_role=AgentRole.CRITIC,
            content=critic_result,
            timestamp=datetime.now(),
            round_id=session.current_round,
            paper_id=session.paper_id
        ))
        
        # 第四轮：协调员Agent整合所有结果
        print("🎯 协调员Agent开始整合结果...")
        coordinator_context = f"""
{context}

=== 各专家Agent的分析结果 ===

分析师分析：
{analyst_result}

总结师总结：
{summarizer_result}

评论员评价：
{critic_result}
"""
        coordinator_result = self.call_agent_api(AgentRole.COORDINATOR, 
                                                "请整合所有专家的分析，生成最终的论文解读报告", 
                                                coordinator_context)
        results['coordinator'] = coordinator_result
        
        session.messages.append(AgentMessage(
            agent_role=AgentRole.COORDINATOR,
            content=coordinator_result,
            timestamp=datetime.now(),
            round_id=session.current_round,
            paper_id=session.paper_id
        ))
        
        # 更新会话分析结果
        session.analysis_result = {
            'round': session.current_round,
            'timestamp': datetime.now().isoformat(),
            'results': results,
            'user_query': user_query
        }
        
        return session.analysis_result
    
    def get_session_history(self, session_id: str) -> Optional[Dict]:
        """获取会话历史"""
        if session_id not in self.sessions:
            return None
        
        session = self.sessions[session_id]
        return {
            'session_id': session_id,
            'paper_id': session.paper_id,
            'current_round': session.current_round,
            'created_at': session.created_at.isoformat(),
            'messages': [
                {
                    'agent_role': msg.agent_role.value,
                    'content': msg.content,
                    'timestamp': msg.timestamp.isoformat(),
                    'round_id': msg.round_id
                }
                for msg in session.messages
            ],
            'latest_analysis': session.analysis_result
        }
    
    def continue_analysis(self, session_id: str, new_query: str, paper_content: str, paper_metadata: Dict) -> Dict:
        """继续分析（基于之前的结果进行深入分析）"""
        if session_id not in self.sessions:
            raise ValueError("会话不存在")
        
        session = self.sessions[session_id]
        
        # 构建包含历史分析的上下文
        context = f"""
标题: {paper_metadata.get('title', '未知')}
作者: {paper_metadata.get('authors', '未知')}
摘要: {paper_metadata.get('abstract', '无摘要')}

论文内容:
{paper_content[:6000]}

=== 之前的分析历史 ===
"""
        
        # 添加历史分析结果
        if session.analysis_result:
            prev_results = session.analysis_result.get('results', {})
            for role, result in prev_results.items():
                context += f"\n{role}的分析：\n{result[:1000]}...\n"
        
        # 运行新一轮分析
        return self.run_multi_agent_analysis(session_id, paper_content, paper_metadata, new_query)
    
    def generate_final_report(self, session_id: str) -> str:
        """生成最终的综合报告"""
        if session_id not in self.sessions:
            return "会话不存在"
        
        session = self.sessions[session_id]
        if not session.analysis_result:
            return "还没有分析结果"
        
        results = session.analysis_result.get('results', {})
        
        report = f"""# 多Agent论文分析报告

**论文ID**: {session.paper_id}
**分析时间**: {session.analysis_result.get('timestamp', 'Unknown')}
**分析轮次**: {session.current_round}

---

## 🔍 分析师观点
{results.get('analyst', '暂无分析')}

---

## 📝 总结师观点  
{results.get('summarizer', '暂无总结')}

---

## 🤔 评论员观点
{results.get('critic', '暂无评论')}

---

## 🎯 协调员综合结论
{results.get('coordinator', '暂无综合结论')}

---

*本报告由多Agent协作系统生成，包含多个专业AI的不同视角分析*
"""
        
        return report
    
    def generate_mindmap(self, session_id: str) -> str:
        """生成思维导图的Mermaid代码"""
        if session_id not in self.sessions:
            print(f"会话 {session_id} 不存在")
            return ""
        
        session = self.sessions[session_id]
        if not session.analysis_result:
            print("没有分析结果")
            return ""
        
        results = session.analysis_result.get('results', {})
        print(f"分析结果键: {list(results.keys())}")
        
        # 打印每个Agent的输出长度，用于调试
        for agent, content in results.items():
            print(f"{agent} 输出长度: {len(content) if content else 0}")
            if content:
                print(f"{agent} 前100字符: {content[:100]}...")
        
        # 提取关键信息构建思维导图
        mindmap_data = {
            'title': f'论文分析报告',
            'agents': {
                'analyst': self._extract_key_points(results.get('analyst', ''), '分析师'),
                'summarizer': self._extract_key_points(results.get('summarizer', ''), '总结师'),
                'critic': self._extract_key_points(results.get('critic', ''), '评论员'),
                'coordinator': self._extract_key_points(results.get('coordinator', ''), '协调员')
            }
        }
        
        print(f"提取的关键点数量: {[(k, len(v)) for k, v in mindmap_data['agents'].items()]}")
        for agent, points in mindmap_data['agents'].items():
            if points:
                print(f"{agent} 关键点: {points[:2]}")  # 只打印前2个
        
        # 生成Mermaid思维导图代码 - 使用flowchart格式
        mermaid_code = """flowchart TD
    A[📚 论文分析报告] --> B[🔍 分析师观点]
    A --> C[📝 总结师观点]
    A --> D[🤔 评论员观点]
    A --> E[🎯 协调员综合]
"""
        
        # 为每个Agent添加详细信息节点
        agent_configs = [
            ('analyst', 'B', '🔍 分析师', mindmap_data['agents'].get('analyst', [])),
            ('summarizer', 'C', '📝 总结师', mindmap_data['agents'].get('summarizer', [])),
            ('critic', 'D', '🤔 评论员', mindmap_data['agents'].get('critic', [])),
            ('coordinator', 'E', '🎯 协调员', mindmap_data['agents'].get('coordinator', []))
        ]
        
        node_counter = 1
        all_detail_nodes = []
        
        for agent_type, parent_node, agent_name, points in agent_configs:
            agent_detail_nodes = []
            
            if points:
                for i, point in enumerate(points[:4]):  # 每个Agent最多4个要点
                    node_id = f"{parent_node}{node_counter}"
                    clean_point = self._clean_text_for_mermaid(point)
                    
                    # 根据不同Agent类型添加不同的节点样式
                    if agent_type == 'analyst':
                        mermaid_code += f"    {parent_node} --> {node_id}([{clean_point}])\n"
                    elif agent_type == 'summarizer':
                        mermaid_code += f"    {parent_node} --> {node_id}[{clean_point}]\n"
                    elif agent_type == 'critic':
                        mermaid_code += f"    {parent_node} --> {node_id}{{{clean_point}}}\n"
                    else:  # coordinator
                        mermaid_code += f"    {parent_node} --> {node_id}(({clean_point}))\n"
                    
                    agent_detail_nodes.append(node_id)
                    node_counter += 1
            else:
                # 如果没有提取到要点，添加默认节点
                node_id = f"{parent_node}{node_counter}"
                default_text = f"{agent_name}分析"
                mermaid_code += f"    {parent_node} --> {node_id}[{default_text}]\n"
                agent_detail_nodes.append(node_id)
                node_counter += 1
            
            all_detail_nodes.extend(agent_detail_nodes)
        
        # 添加Agent之间的协作关系
        if len(agent_configs) >= 4:
            mermaid_code += f"""
    %% Agent协作关系
    B -.-> C
    C -.-> D  
    D -.-> E
    B -.-> E
"""
        
        # 添加总结节点
        if all_detail_nodes:
            mermaid_code += f"""
    %% 最终输出
    E --> F[📋 分析结论]
    E --> G[💡 应用价值]
    E --> H[🔮 未来方向]
"""
        
        # 添加样式定义
        mermaid_code += """
    %% 样式定义
    classDef mainNode fill:#667eea,stroke:#667eea,stroke-width:3px,color:#fff
    classDef analyst fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#0d47a1
    classDef summarizer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#4a148c
    classDef critic fill:#fff8e1,stroke:#f57c00,stroke-width:2px,color:#e65100
    classDef coordinator fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#1b5e20
    classDef output fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#880e4f
    
    class A mainNode
    class B analyst
    class C summarizer
    class D critic
    class E coordinator
    class F,G,H output
"""
        
        print(f"生成的思维导图代码长度: {len(mermaid_code)}")
        return mermaid_code
    
    def _extract_key_points(self, text: str, agent_type: str) -> List[str]:
        """从Agent输出中提取关键要点"""
        if not text:
            return []
        
        # 根据不同Agent类型提取不同的关键信息
        key_points = []
        lines = text.split('\n')
        
        # 定义不同Agent的关键词
        keywords_by_agent = {
            '分析师': ['问题', '方法', '创新', '贡献', '技术', '实验', '结果', '发现'],
            '总结师': ['核心', '主要', '关键', '重点', '内容', '结论', '总结', '概括'],
            '评论员': ['局限', '问题', '不足', '改进', '建议', '批评', '缺陷', '风险'],
            '协调员': ['综合', '整体', '总体', '全面', '平衡', '结合', '统一', '协调']
        }
        
        target_keywords = keywords_by_agent.get(agent_type, [])
        
        # 第一轮：查找标题和列表项
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 查找各种格式的要点
            if (line.startswith('#') or 
                line.startswith('-') or 
                line.startswith('*') or
                line.startswith('•') or
                any(line.startswith(f'{i}.') for i in range(1, 10)) or
                '**' in line):
                
                # 清理格式符号
                clean_line = line
                for symbol in ['#', '*', '-', '•', '**']:
                    clean_line = clean_line.replace(symbol, '')
                
                # 移除数字编号
                import re
                clean_line = re.sub(r'^\d+\.?\s*', '', clean_line).strip()
                
                if len(clean_line) > 8 and len(clean_line) < 80:
                    key_points.append(clean_line)
                    
                if len(key_points) >= 6:
                    break
        
        # 第二轮：如果没有找到足够的结构化内容，查找包含关键词的句子
        if len(key_points) < 3:
            sentences = []
            # 按句号分割
            for sentence in text.replace('\n', ' ').split('。'):
                sentence = sentence.strip()
                if len(sentence) > 15 and len(sentence) < 100:
                    # 检查是否包含目标关键词
                    if any(keyword in sentence for keyword in target_keywords):
                        sentences.append(sentence + '。')
                        
            key_points.extend(sentences[:6-len(key_points)])
        
        # 第三轮：如果还是不够，提取前几句重要的句子
        if len(key_points) < 2:
            important_sentences = []
            for sentence in text.replace('\n', ' ').split('。'):
                sentence = sentence.strip()
                if (len(sentence) > 20 and 
                    any(word in sentence for word in ['重要', '关键', '主要', '核心', '显著', '明显', '发现', '结论'])):
                    important_sentences.append(sentence + '。')
                    
            key_points.extend(important_sentences[:4-len(key_points)])
        
        # 最后确保至少有一些内容
        if not key_points and text:
            # 取前两句话作为兜底
            sentences = text.split('。')[:2]
            for sentence in sentences:
                if len(sentence.strip()) > 10:
                    key_points.append(sentence.strip() + '。')
        
        return key_points[:5]  # 最多返回5个要点
    
    def _clean_text_for_mermaid(self, text: str) -> str:
        """清理文本以适配Mermaid格式"""
        if not text:
            return "暂无内容"
            
        # 移除markdown格式符号
        text = text.replace('**', '').replace('*', '').replace('#', '')
        text = text.replace('[', '').replace(']', '').replace('`', '')
        
        # 处理引号和括号 - 但保留有意义的括号内容
        import re
        # 移除空括号
        text = re.sub(r'\(\s*\)', '', text)
        text = re.sub(r'\[\s*\]', '', text)
        text = re.sub(r'\{\s*\}', '', text)
        
        # 移除换行符和多余空格
        text = text.replace('\n', ' ').replace('\r', ' ')
        text = ' '.join(text.split())  # 移除多余空格
        
        # 移除常见的无意义开头
        prefixes_to_remove = ['本文', '该论文', '这篇论文', '文章', '研究', '作者']
        for prefix in prefixes_to_remove:
            if text.startswith(prefix):
                text = text[len(prefix):].lstrip('：:：。，')
                break
        
        # 移除句末标点，在思维导图中不需要
        text = text.rstrip('。！？；：，')
        
        # 处理长文本，智能截断
        if len(text) > 25:
            # 尝试在合适的位置截断
            for i in range(20, min(25, len(text))):
                if text[i] in '，。、；：':
                    text = text[:i]
                    break
            else:
                # 如果没有找到合适的截断点，在词边界截断
                words = text[:22].split()
                if len(words) > 1:
                    text = ' '.join(words[:-1]) + '...'
                else:
                    text = text[:22] + '...'
        
        # 确保不为空
        text = text.strip()
        if not text or text == '...':
            return "相关分析"
            
        return text
