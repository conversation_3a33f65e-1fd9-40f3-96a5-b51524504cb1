import os
import json
import hashlib
from typing import List, Dict, Optional
import fitz  # PyMuPDF
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import Chroma
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.docstore.document import Document
import requests
import PyPDF2


def parse_pdf_with_pymupdf(file_path: str) -> List[Dict]:
    """
    使用PyMuPDF从PDF中提取文本块及其元数据（页码和边界框）。
    """
    chunks = []
    try:
        doc = fitz.open(file_path)
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            blocks = page.get_text("dict", flags=fitz.TEXTFLAGS_TEXT)
            for block in blocks["blocks"]:
                if block["type"] == 0:  # Text block
                    for line in block["lines"]:
                        for span in line["spans"]:
                            chunks.append({
                                "text": span["text"],
                                "page_num": page_num + 1,
                                "bbox": list(span["bbox"])
                            })
    except Exception as e:
        print(f"Error parsing PDF with PyMuPDF: {e}")
    return chunks

class RAGService:
    """RAG服务类，处理文档向量化和检索"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.embeddings = HuggingFaceEmbeddings(
            model_name="sentence-transformers/all-MiniLM-L6-v2",
            model_kwargs={'device': 'cpu'}
        )
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )
        
        # 向量数据库存储目录
        self.vector_db_dir = "vector_store"
        os.makedirs(self.vector_db_dir, exist_ok=True)
    
    def get_paper_vector_store_path(self, paper_id: int) -> str:
        """获取论文向量数据库路径"""
        return os.path.join(self.vector_db_dir, f"paper_{paper_id}")
    
    def create_vector_store_from_pdf(self, paper_id: int, file_path: str) -> bool:
        """从PDF文件创建向量数据库，包含详细的元数据"""
        try:
            # 使用PyMuPDF解析PDF
            pdf_chunks = parse_pdf_with_pymupdf(file_path)
            
            # 将块转换为LangChain的Document对象
            documents = []
            for chunk in pdf_chunks:
                metadata = {
                    "source": os.path.basename(file_path),
                    "page_num": chunk["page_num"],
                    "bbox": json.dumps(chunk["bbox"]) # 将bbox转换为JSON字符串以便存储
                }
                documents.append(Document(page_content=chunk["text"], metadata=metadata))

            # 分割长文本块（如果需要）
            docs = self.text_splitter.split_documents(documents)

            # 创建向量数据库
            vector_store_path = self.get_paper_vector_store_path(paper_id)
            vectorstore = Chroma.from_documents(
                documents=docs,
                embedding=self.embeddings,
                persist_directory=vector_store_path
            )
            vectorstore.persist()
            
            print(f"为论文 {paper_id} 创建向量数据库成功，共 {len(docs)} 个文档片段")
            return True
            
        except Exception as e:
            print(f"从PDF创建向量数据库失败: {e}")
            return False

    def load_vector_store(self, paper_id: int) -> Optional[Chroma]:
        """加载已存在的向量数据库"""
        try:
            vector_store_path = self.get_paper_vector_store_path(paper_id)
            if not os.path.exists(vector_store_path):
                return None
            
            vectorstore = Chroma(
                persist_directory=vector_store_path,
                embedding_function=self.embeddings
            )
            return vectorstore
            
        except Exception as e:
            print(f"加载向量数据库失败: {e}")
            return None
    
    def search_relevant_documents(self, paper_id: int, query: str, k: int = 5) -> List[Dict]:
        """搜索相关文档片段，并返回内容和元数据"""
        try:
            vectorstore = self.load_vector_store(paper_id)
            if not vectorstore:
                return []
            
            # 搜索相关文档，并返回元数据
            results = vectorstore.similarity_search_with_score(query, k=k)
            
            # 格式化返回结果
            formatted_results = []
            for doc, score in results:
                metadata = doc.metadata
                # 解析bbox
                if "bbox" in metadata and isinstance(metadata["bbox"], str):
                    metadata["bbox"] = json.loads(metadata["bbox"])
                
                formatted_results.append({
                    "text": doc.page_content,
                    **metadata
                })

            return formatted_results
            
        except Exception as e:
            print(f"搜索相关文档失败: {e}")
            return []
    
    def generate_hidden_pre_read(self, paper_id: int, pdf_content: str, prompt_content: str) -> Optional[str]:
        """使用配置的模型生成隐藏的预阅读"""
        try:
            # 使用专门的隐藏预阅读模型配置
            hidden_pre_read_config = self.config.get('hidden_pre_read_model', {})
            
            # 构建API请求
            api_messages = [
                {"role": "system", "content": prompt_content},
                {"role": "user", "content": f"请分析以下论文内容：\n\n{pdf_content}"}
            ]

            headers = {
                'Authorization': f'Bearer {hidden_pre_read_config.get("api_key", "")}',
                'Content-Type': 'application/json',
                'HTTP-Referer': 'http://localhost:5001',
                'X-Title': 'DocuMancer'
            }

            payload = {
                'model': hidden_pre_read_config.get('model', 'google/gemini-2.5-flash-lite-preview-06-17'),
                'messages': api_messages,
                'max_tokens': hidden_pre_read_config.get('max_tokens', 131072),
                'temperature': hidden_pre_read_config.get('temperature', 0.3),
                'stream': False
            }

            print(f"正在使用{hidden_pre_read_config.get('model', 'default')}生成隐藏预阅读...")
            
            response = requests.post(
                f"{hidden_pre_read_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    hidden_pre_read = result['choices'][0]['message']['content']
                    
                    # 保存隐藏预阅读到特殊目录
                    hidden_pre_read_dir = "hidden_pre_reads"
                    os.makedirs(hidden_pre_read_dir, exist_ok=True)
                    hidden_pre_read_path = os.path.join(hidden_pre_read_dir, f"hidden_pre_read_{paper_id}.md")
                    
                    with open(hidden_pre_read_path, 'w', encoding='utf-8') as f:
                        f.write(hidden_pre_read)
                    
                    print(f"隐藏预阅读生成成功: {hidden_pre_read_path}")
                    return hidden_pre_read
                else:
                    print(f"隐藏预阅读API响应格式错误: {result}")
                    return None
            else:
                print(f"隐藏预阅读API调用失败: {response.status_code}, {response.text}")
                return None

        except Exception as e:
            print(f"生成隐藏预阅读错误: {e}")
            return None
    
    def get_or_create_rag_context(self, paper_id: int, paper_info: Dict) -> bool:
        """获取或创建RAG上下文"""
        try:
            # 1. 检查是否已有向量数据库
            vector_store_path = self.get_paper_vector_store_path(paper_id)
            if os.path.exists(vector_store_path):
                 print(f"论文 {paper_id} 的向量数据库已存在")
                 return True

            # 2. 如果没有，从PDF创建
            print(f"论文 {paper_id} 的向量数据库不存在，正在从PDF创建...")
            
            # 检查PDF文件
            file_path = paper_info.get('file_path')
            if not file_path or not os.path.exists(file_path):
                print(f"论文 {paper_id} 的PDF文件不存在")
                return False
            
            return self.create_vector_store_from_pdf(paper_id, file_path)
            
        except Exception as e:
            print(f"获取或创建RAG上下文失败: {e}")
            return False
    
    def delete_hidden_pre_read(self, paper_id: int) -> bool:
        """删除隐藏预阅读文件（当用户生成正式预阅读时调用）"""
        try:
            hidden_pre_read_path = os.path.join('hidden_pre_reads', f"hidden_pre_read_{paper_id}.md")
            if os.path.exists(hidden_pre_read_path):
                os.remove(hidden_pre_read_path)
                print(f"已删除隐藏预阅读文件: {hidden_pre_read_path}")
                
                # 同时删除对应的向量数据库，强制重建
                vector_store_path = self.get_paper_vector_store_path(paper_id)
                if os.path.exists(vector_store_path):
                    import shutil
                    shutil.rmtree(vector_store_path)
                    print(f"已删除隐藏预阅读的向量数据库: {vector_store_path}")
                
                return True
            return False
        except Exception as e:
            print(f"删除隐藏预阅读失败: {e}")
            return False
    
    def create_rag_prompt(self, query: str, context_docs: List[Dict], paper_info: Dict) -> str:
        """创建包含RAG上下文的提示词"""
        context_text = "\n\n".join([f"文档片段 {i+1}:\n{doc['text']}" for i, doc in enumerate(context_docs)])
        
        rag_prompt = f"""你是一个专业的学术论文阅读助手。请基于以下预阅读文档内容来回答用户的问题。

论文信息：
标题：{paper_info.get('title', '未知')}
作者：{paper_info.get('authors', '未知')}
摘要：{paper_info.get('abstract', '无摘要')}

相关文档内容：
{context_text}

请遵循以下准则：
1. 主要基于提供的文档内容回答问题
2. 提供准确、专业的学术分析
3. 用清晰易懂的语言解释复杂概念
4. 如果问题超出文档范围，请明确说明并基于你的知识进行补充
5. 保持客观、中性的学术态度

用户问题：{query}"""

        return rag_prompt

    def search_multi_doc_relevant_documents(self, main_paper_id: int, reference_paper_ids: List[int], query: str, k: int = 8) -> List[Dict]:
        """在多篇论文中搜索相关文档"""
        try:
            all_relevant_docs = []
            
            # 搜索主论文
            main_docs = self.search_relevant_documents(main_paper_id, query, k=k//2)
            for doc in main_docs:
                doc['paper_id'] = main_paper_id
                doc['is_main'] = True
                all_relevant_docs.append(doc)

            # 搜索参考论文
            docs_per_ref_paper = max(1, k // (2 * len(reference_paper_ids))) if reference_paper_ids else 0
            
            for ref_paper_id in reference_paper_ids:
                ref_docs = self.search_relevant_documents(ref_paper_id, query, k=docs_per_ref_paper)
                for doc in ref_docs:
                    doc['paper_id'] = ref_paper_id
                    doc['is_main'] = False
                    all_relevant_docs.append(doc)
            
            # 这里可以根据某种策略（例如分数）对 all_relevant_docs 进行排序和筛选
            # 目前只是简单合并
            
            print(f"多文档RAG检索到 {len(all_relevant_docs)} 个相关文档片段")
            return all_relevant_docs[:k]
            
        except Exception as e:
            print(f"多文档RAG搜索错误: {e}")
            return []
    
    def create_multi_doc_rag_prompt(self, query: str, relevant_docs: List[Dict], main_paper: Dict, reference_papers: List[Dict]) -> str:
        """创建多文档RAG增强的提示词"""
        
        # 构建文档上下文
        context_text = ""
        for i, doc in enumerate(relevant_docs):
            paper_id = doc.get('paper_id', 'unknown')
            is_main = doc.get('is_main', False)
            source_tag = f"[主论文-{paper_id}]" if is_main else f"[参考论文-{paper_id}]"
            context_text += f"文档片段 {i+1} {source_tag}:\n{doc['text']}\n\n"

        # 构建论文信息
        main_paper_info = f"""
当前论文（主要视角）：
标题：{main_paper.get('title', '未知')}
作者：{main_paper.get('authors', '未知')}
来源：{main_paper.get('source', '未知')}
摘要：{main_paper.get('abstract', '无摘要')}"""

        reference_papers_info = ""
        if reference_papers:
            reference_papers_info = "\n\n参考论文："
            for i, ref_paper in enumerate(reference_papers, 1):
                reference_papers_info += f"""
{i}. {ref_paper.get('title', '未知')}
   作者：{ref_paper.get('authors', '未知')}
   来源：{ref_paper.get('source', '未知')}
   摘要：{ref_paper.get('abstract', '无摘要')}"""

        rag_prompt = f"""你是一个专业的学术论文阅读助手，现在正在进行跨文档分析。请站在当前论文的角度，结合参考论文来回答用户的问题。

{main_paper_info}
{reference_papers_info}

相关文档内容：
{context_text}

请遵循以下准则：
1. 以当前论文为主要视角，将其作为核心分析对象
2. 将参考论文作为对比、补充或支持材料，体现梯度对待
3. 在回答中明确指出当前论文与参考论文之间的异同点、关联性或对比
4. 提供准确、专业的学术分析，体现以当前论文为中心的多文档视角
5. 用清晰易懂的语言解释复杂概念
6. 如果问题涉及文档中没有的内容，请明确说明并基于你的知识进行补充
7. 保持客观、中性的学术态度
8. 在引用不同论文内容时，可以参考文档片段中的来源标识，并明确区分当前论文和参考论文的内容

用户问题：{query}"""

        return rag_prompt 