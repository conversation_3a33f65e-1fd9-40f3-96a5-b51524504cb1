# 多文档对话PDF传输改进

## 问题描述

在多论文对话功能中，LLM API只能获取"当前论文"的PDF文件，无法获取"参考论文"的PDF文件。这导致LLM无法同时查看和分析多篇论文的内容，限制了多文档分析的能力。

## 根本原因

原有的实现中，PDF文件只在`is_first_user_message`（首次对话）时传输给LLM API：

```python
if is_first_user_message:
    all_papers = [main_paper] + reference_papers
    for paper in all_papers:
        # 传输PDF文件
```

这导致：
1. 首次对话后，LLM无法再访问PDF文件
2. 只有主论文的PDF被传输，参考论文的PDF被忽略
3. 多文档分析能力受限

## 解决方案

### 修改内容

1. **移除首次对话限制**：将PDF传输从`if is_first_user_message`条件中移除
2. **始终包含所有论文PDF**：在每次对话中都传输所有论文（主论文+参考论文）的PDF
3. **始终启用文件解析插件**：确保LLM能够解析PDF内容

### 修改的函数

1. `call_llm_api_with_multi_doc_rag()` - 非流式多文档RAG
2. `call_llm_api_stream_with_multi_doc_rag()` - 流式多文档RAG

### 代码变更

**修改前：**
```python
if is_first_user_message:
    all_papers = [main_paper] + reference_papers
    for paper in all_papers:
        # 传输PDF文件

if is_first_user_message and isinstance(rag_messages[-1]['content'], list):
    payload['plugins'] = [...]
```

**修改后：**
```python
# 在多文档对话中，始终包含所有论文的PDF文件
all_papers = [main_paper] + reference_papers
for paper in all_papers:
    # 传输PDF文件

# 在多文档对话中，始终启用文件解析插件
if isinstance(rag_messages[-1]['content'], list):
    payload['plugins'] = [...]
```

## 技术细节

### PDF传输机制

1. **Base64编码**：将PDF文件编码为Base64字符串
2. **Data URL格式**：使用`data:application/pdf;base64,{base64_data}`格式
3. **OpenRouter插件**：使用`file-parser`插件解析PDF内容
4. **多模态消息**：PDF文件作为多模态消息的一部分与文本内容一起发送

### 性能考虑

- **文件大小**：每次对话都会传输所有PDF文件，可能增加API调用时间
- **内存使用**：Base64编码会增加约33%的数据大小
- **API限制**：需要考虑OpenRouter的API限制和超时设置

## 影响范围

### 正面影响

1. **增强多文档分析能力**：LLM可以同时查看和分析多篇论文
2. **提高对话质量**：基于完整PDF内容的回答更准确
3. **支持复杂查询**：可以回答涉及多篇论文的复杂问题

### 潜在影响

1. **API调用时间增加**：每次对话都需要传输多个PDF文件
2. **成本增加**：更多的数据传输可能增加API调用成本
3. **内存使用增加**：需要处理更多的PDF数据

## 测试验证

通过测试脚本验证了修改的正确性：

- ✅ 首次对话和后续对话都包含所有PDF文件
- ✅ 文件解析插件在每次对话中都启用
- ✅ 所有论文（主论文+参考论文）的PDF都被传输

## 使用建议

1. **合理选择参考论文数量**：避免选择过多参考论文导致性能问题
2. **监控API性能**：关注API调用时间和成功率
3. **优化PDF文件大小**：考虑压缩PDF文件以减少传输时间

## 后续优化方向

1. **智能PDF缓存**：实现PDF文件的智能缓存机制
2. **增量传输**：只传输新增或修改的PDF文件
3. **压缩优化**：实现PDF文件的智能压缩
4. **异步处理**：实现PDF传输的异步处理机制 