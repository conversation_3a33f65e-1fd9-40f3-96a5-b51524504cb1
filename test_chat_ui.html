<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI流式消息动画测试 - DocuMancer</title>
    <link rel="stylesheet" href="static/styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            text-align: center;
            color: var(--primary-color);
            margin-bottom: 30px;
        }
        
        .test-button {
            background: var(--success-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: var(--success-hover);
            transform: translateY(-2px);
        }
        
        .demo-message {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">AI流式消息动画优化测试</h1>
        
        <div class="demo-message">
            <h3>测试功能：</h3>
            <ul>
                <li>✅ 优化的打字机效果</li>
                <li>✅ 智能文本分割</li>
                <li>✅ 流畅的字符动画</li>
                <li>✅ 特殊元素动画</li>
                <li>✅ 错误处理动画</li>
                <li>✅ 响应式设计</li>
                <li>✅ 无障碍支持</li>
            </ul>
        </div>
        
        <div style="text-align: center;">
            <button class="test-button" onclick="testShortMessage()">测试短消息</button>
            <button class="test-button" onclick="testLongMessage()">测试长消息</button>
            <button class="test-button" onclick="testCodeMessage()">测试代码消息</button>
            <button class="test-button" onclick="testMarkdownMessage()">测试Markdown消息</button>
            <button class="test-button" onclick="testErrorAnimation()">测试错误动画</button>
        </div>
        
        <div id="testChatMessages" class="chat-messages" style="height: 400px; border: 1px solid #e0e0e0; border-radius: 8px; padding: 15px; margin-top: 20px;">
            <div class="message assistant">
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    <div class="streaming-content">
                        欢迎测试AI流式消息动画效果！点击上方按钮体验不同的动画效果。
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function createStreamingMessage(content, type = 'content') {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant';
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = '🤖';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.innerHTML = '<div class="streaming-content"></div>';
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);
            
            const chatMessages = document.getElementById('testChatMessages');
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            const streamingContent = messageContent.querySelector('.streaming-content');
            
            // 模拟真实的流式输出
            let fullResponse = '';
            let chunkIndex = 0;
            
            // 将内容分割成小块，模拟流式传输
            const chunks = splitContentIntoChunks(content);
            
            const interval = setInterval(() => {
                if (chunkIndex < chunks.length) {
                    fullResponse += chunks[chunkIndex];
                    
                    // 处理Markdown
                    const processedText = postProcessMarkdown(renderMarkdown(fullResponse));
                    updateStreamingContent(processedText, streamingContent);
                    
                    chunkIndex++;
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                } else {
                    clearInterval(interval);
                    messageDiv.classList.add('completed');
                }
            }, 100); // 每100ms添加一个块
            
            return messageDiv;
        }
        
        // 将内容分割成小块
        function splitContentIntoChunks(content) {
            const chunks = [];
            const words = content.split(' ');
            let currentChunk = '';
            
            for (let i = 0; i < words.length; i++) {
                currentChunk += words[i] + ' ';
                
                // 每3-5个词作为一个块
                if (i % 4 === 3 || i === words.length - 1) {
                    chunks.push(currentChunk.trim());
                    currentChunk = '';
                }
            }
            
            return chunks;
        }
        
        // 简化的Markdown渲染函数
        function renderMarkdown(text) {
            return text
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/`([^`]+)`/g, '<code>$1</code>')
                .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                .replace(/^\* (.*$)/gm, '<li>$1</li>')
                .replace(/^- (.*$)/gm, '<li>$1</li>')
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
                .replace(/\n/g, '<br>');
        }
        
        // 简化的后处理函数
        function postProcessMarkdown(html) {
            html = html.replace(/(<li[^>]*>[^<]*<\/li>(?:<br>)*)+/g, function(match) {
                const items = match.replace(/<br>/g, '');
                return '<ul>' + items + '</ul>';
            });
            
            html = html.replace(/<br><\/ul>/g, '</ul>');
            html = html.replace(/<\/h[1-6]><br>/g, function(match) {
                return match.replace('<br>', '');
            });
            
            return html;
        }
        
        // 更新流式内容函数
        function updateStreamingContent(newHtml, container) {
            if (!container.children.length) {
                container.innerHTML = newHtml;
                return;
            }
            
            const currentHtml = container.innerHTML;
            
            if (currentHtml === newHtml) {
                return;
            }
            
            const diff = findHtmlDifference(currentHtml, newHtml);
            
            if (diff.type === 'append') {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = diff.newContent;
                
                while (tempDiv.firstChild) {
                    container.appendChild(tempDiv.firstChild);
                }
                
                animateNewContent(container, diff.newContent);
            } else {
                container.innerHTML = newHtml;
            }
        }
        
        // 查找HTML差异
        function findHtmlDifference(oldHtml, newHtml) {
            if (newHtml.startsWith(oldHtml) && newHtml.length > oldHtml.length) {
                return {
                    type: 'append',
                    newContent: newHtml.substring(oldHtml.length)
                };
            }
            
            return {
                type: 'replace',
                newContent: newHtml
            };
        }
        
        // 为新内容添加动画
        function animateNewContent(container, newContent) {
            const lastChild = container.lastElementChild;
            
            if (lastChild && lastChild.nodeType === Node.ELEMENT_NODE) {
                lastChild.style.opacity = '0';
                lastChild.style.transform = 'translateY(10px)';
                lastChild.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                
                requestAnimationFrame(() => {
                    lastChild.style.opacity = '1';
                    lastChild.style.transform = 'translateY(0)';
                });
            } else if (lastChild && lastChild.nodeType === Node.TEXT_NODE) {
                const text = lastChild.textContent;
                if (text && text.trim()) {
                    animateNewText(lastChild, text);
                }
            }
        }
        
        // 为新文本添加动画
        function animateNewText(textNode, text) {
            const wrapper = document.createElement('span');
            wrapper.className = 'typing-text';
            
            textNode.parentNode.insertBefore(wrapper, textNode);
            textNode.parentNode.removeChild(textNode);
            
            let charIndex = 0;
            const showNextChar = () => {
                if (charIndex < text.length) {
                    const char = text[charIndex];
                    wrapper.textContent += char;
                    charIndex++;
                    
                    let delay = 8;
                    if (char === ' ' || char === '\n') {
                        delay = 3;
                    } else if (char === '.' || char === '!' || char === '?') {
                        delay = 20;
                    } else if (char === ',' || char === ';' || char === ':') {
                        delay = 15;
                    }
                    
                    requestAnimationFrame(() => {
                        setTimeout(showNextChar, delay);
                    });
                } else {
                    wrapper.classList.remove('typing-text');
                }
            };
            showNextChar();
        }
        
        function testShortMessage() {
            createStreamingMessage("这是一个短消息测试，展示基本的打字机效果。");
        }
        
        function testLongMessage() {
            createStreamingMessage("这是一个较长的消息测试，用于展示智能文本分割功能。当文本内容较长时，系统会自动将文本分割成更小的段落，以提高动画的流畅性和性能。这样可以确保即使在处理大量文本时，用户仍然能够享受到流畅的打字机效果。");
        }
        
        function testCodeMessage() {
            const codeMessage = `这是一个包含代码的消息：

\`\`\`python
def hello_world():
    print("Hello, World!")
    return "Success"
\`\`\`

代码块会有特殊的动画效果。`;
            createStreamingMessage(codeMessage);
        }
        
        function testMarkdownMessage() {
            const markdownMessage = `这是一个**Markdown格式**的消息测试：

## 标题示例
- 列表项1
- 列表项2
- 列表项3

> 这是一个引用块，用于展示引用样式。

[链接示例](https://example.com)`;
            createStreamingMessage(markdownMessage);
        }
        
        function testErrorAnimation() {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant error-message';
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = '🤖';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            messageContent.textContent = '这是一个错误消息示例，展示错误动画效果。';
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);
            
            const chatMessages = document.getElementById('testChatMessages');
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            // 触发错误动画
            messageDiv.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
                messageDiv.style.animation = '';
            }, 500);
        }
    </script>
</body>
</html> 