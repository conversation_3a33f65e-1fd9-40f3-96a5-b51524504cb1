from flask import Flask, render_template, request, jsonify, redirect, url_for, send_file, Response, stream_with_context
import os
import json
import requests
import PyPDF2
import hashlib
from difflib import SequenceMatcher
import re
import base64

from datetime import datetime
import xml.etree.ElementTree as ET
from rag_service import RAGService
from models import db, Paper, PaperHistory, DialogueMessage, Citation
from flask_migrate import Migrate
import threading
import time
from multi_agent_system import MultiAgentReader

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 加载配置
def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("警告：config.json文件未找到，使用默认配置")
        return {
            "llm": {
                "provider": "openrouter",
                "base_url": "https://openrouter.ai/api/v1",
                "model": "google/gemini-2.5-flash-lite-preview-06-17",
                "max_tokens": 2048,
                "temperature": 0.7
            }
        }
    except json.JSONDecodeError:
        print("错误：config.json格式不正确")
        return {}

# 全局配置
CONFIG = load_config()

# 配置数据库
app.config['SQLALCHEMY_DATABASE_URI'] = CONFIG.get('database', {}).get('uri', 'sqlite:///documancer.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 初始化数据库
db.init_app(app)
migrate = Migrate(app, db)

# 初始化RAG服务
rag_service = RAGService(CONFIG)

# 初始化多Agent系统
multi_agent_reader = MultiAgentReader(CONFIG)

with app.app_context():
    # db.create_all() # Replaced by Flask-Migrate
    pass

def call_llm_api(messages, paper_context=None):
    """调用LLM API进行对话"""
    try:
        llm_config = CONFIG.get('llm', {})
        
        # 构建系统提示词
        system_prompt = """你是一个专业的学术论文阅读助手。你的任务是帮助用户理解和分析学术论文。

请遵循以下准则：
1. 提供准确、专业的学术分析
2. 用清晰易懂的语言解释复杂概念
3. 结合论文内容回答问题
4. 如果问题超出论文范围，请明确说明
5. 保持客观、中性的学术态度

当前论文信息："""

        if paper_context:
            system_prompt += f"""
标题：{paper_context.get('title', '未知')}
作者：{paper_context.get('authors', '未知')}
摘要：{paper_context.get('abstract', '无摘要')}
来源：{paper_context.get('source', '未知')}
"""

        # 构建API请求
        api_messages = [
            {"role": "system", "content": system_prompt}
        ]
        api_messages.extend(messages)

        headers = {
            'Authorization': f'Bearer {llm_config.get("api_key", "")}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:5001',  # OpenRouter要求
            'X-Title': 'DocuMancer'  # OpenRouter要求
        }

        payload = {
            'model': llm_config.get('model', 'google/gemini-2.5-flash-lite-preview-06-17'),
            'messages': api_messages,
            'max_tokens': llm_config.get('max_tokens', 2048),
            'temperature': llm_config.get('temperature', 0.7),
            'stream': False
        }

        print(f"正在调用LLM API: {llm_config.get('model')}")
        
        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                return result['choices'][0]['message']['content']
            else:
                print(f"API响应格式错误: {result}")
                return "抱歉，AI助手暂时无法回复，请稍后再试。"
        else:
            print(f"API调用失败: {response.status_code}, {response.text}")
            return f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"LLM API调用错误: {e}")
        return "抱歉，AI助手遇到了技术问题，请稍后再试。"

def call_llm_api_stream(messages, paper_context=None):
    """调用LLM API进行流式对话"""
    try:
        llm_config = CONFIG.get('llm', {})
        
        # 构建系统提示词
        system_prompt = """你是一个专业的学术论文阅读助手。你的任务是帮助用户理解和分析学术论文。

请遵循以下准则：
1. 提供准确、专业的学术分析
2. 用清晰易懂的语言解释复杂概念
3. 结合论文内容回答问题
4. 如果问题超出论文范围，请明确说明
5. 保持客观、中性的学术态度

当前论文信息："""

        if paper_context:
            system_prompt += f"""
标题：{paper_context.get('title', '未知')}
作者：{paper_context.get('authors', '未知')}
摘要：{paper_context.get('abstract', '无摘要')}
来源：{paper_context.get('source', '未知')}
"""

        # 构建API请求
        api_messages = [
            {"role": "system", "content": system_prompt}
        ]
        api_messages.extend(messages)

        headers = {
            'Authorization': f'Bearer {llm_config.get("api_key", "")}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:5001',  # OpenRouter要求
            'X-Title': 'DocuMancer'  # OpenRouter要求
        }

        payload = {
            'model': llm_config.get('model', 'google/gemini-2.5-flash-lite-preview-06-17'),
            'messages': api_messages,
            'max_tokens': llm_config.get('max_tokens', 32768),
            'temperature': llm_config.get('temperature', 0.7),
            'stream': True
        }

        print(f"正在调用流式LLM API: {llm_config.get('model')}")
        
        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=60,
            stream=True
        )

        if response.status_code == 200:
            # 返回流式响应的生成器
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        if data_str.strip() == '[DONE]':
                            break
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    yield delta['content']
                        except json.JSONDecodeError:
                            continue
        else:
            print(f"流式API调用失败: {response.status_code}, {response.text}")
            yield f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"流式LLM API调用错误: {e}")
        yield "抱歉，AI助手遇到了技术问题，请稍后再试。"

def encode_pdf_to_base64(pdf_path):
    """将PDF文件编码为Base64字符串"""
    try:
        with open(pdf_path, "rb") as pdf_file:
            return base64.b64encode(pdf_file.read()).decode('utf-8')
    except Exception as e:
        print(f"PDF文件编码错误: {e}")
        return None

def call_llm_api_with_rag(messages, paper_id, paper_context=None):
    """调用LLM API进行RAG增强的对话"""
    try:
        # 获取用户最新的问题
        user_message = ""
        for msg in reversed(messages):
            if msg.get('role') == 'user':
                user_message = msg.get('content', '')
                break
        
        if not user_message:
            return call_llm_api(messages, paper_context)
        
        is_first_user_message = len([m for m in messages if m.get('role') == 'user']) == 1
        
        # 确保RAG上下文存在
        if not rag_service.get_or_create_rag_context(paper_id, paper_context):
            print(f"RAG上下文创建失败，回退到普通对话模式")
            return call_llm_api(messages, paper_context)
        
        # 搜索相关文档
        relevant_docs = rag_service.search_relevant_documents(paper_id, user_message, k=5)
        
        if not relevant_docs:
            print(f"未找到相关文档，回退到普通对话模式")
            return call_llm_api(messages, paper_context)
        
        # 创建RAG增强的提示词
        rag_prompt = rag_service.create_rag_prompt(user_message, relevant_docs, paper_context)
        
        # 构建RAG对话消息
        rag_messages = []
        
        # 添加历史对话（除了最后一条用户消息）
        for msg in messages[:-1]:
            if msg.get('role') and msg.get('content'):
                rag_messages.append(msg)
        
        # 构建当前用户消息
        current_user_content = [{"type": "text", "text": rag_prompt}]

        if is_first_user_message and paper_context and paper_context.get('file_path'):
            pdf_path = paper_context['file_path']
            if os.path.exists(pdf_path):
                base64_pdf = encode_pdf_to_base64(pdf_path)
                if base64_pdf:
                    data_url = f"data:application/pdf;base64,{base64_pdf}"
                    current_user_content.append({
                        "type": "file",
                        "file": {
                            "filename": os.path.basename(pdf_path),
                            "file_data": data_url
                        }
                    })
        
        # 添加RAG增强的用户消息
        rag_messages.append({
            'role': 'user',
            'content': current_user_content if len(current_user_content) > 1 else current_user_content[0]['text']
        })
        
        # 调用LLM API
        llm_config = CONFIG.get('llm', {})
        
        headers = {
            'Authorization': f'Bearer {llm_config.get("api_key", "")}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:5001',
            'X-Title': 'DocuMancer'
        }

        payload = {
            'model': llm_config.get('model', 'google/gemini-2.5-flash'),
            'messages': rag_messages,
            'max_tokens': llm_config.get('max_tokens', 32768),
            'temperature': llm_config.get('temperature', 0.7),
            'stream': False
        }

        if is_first_user_message and isinstance(rag_messages[-1]['content'], list) and any(item.get('type') == 'file' for item in rag_messages[-1]['content']):
            payload['plugins'] = [
                {
                    "id": "file-parser",
                    "pdf": { "engine": "native" }
                }
            ]

        print(f"正在调用RAG增强的LLM API: {llm_config.get('model')}")
        
        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                return result['choices'][0]['message']['content']
            else:
                print(f"RAG API响应格式错误: {result}")
                return "抱歉，AI助手暂时无法回复，请稍后再试。"
        else:
            print(f"RAG API调用失败: {response.status_code}, {response.text}")
            return f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"RAG LLM API调用错误: {e}")
        return "抱歉，AI助手遇到了技术问题，请稍后再试。"

def call_llm_api_stream_with_rag(messages, paper_id, paper_context=None):
    """使用RAG调用LLM API（流式）"""
    try:
        llm_config = CONFIG.get('llm', {})
        # RAGService is a global singleton, no need to re-instantiate
        
        # 0. 准备RAG上下文
        paper = db.session.get(Paper, paper_id)
        if not paper:
            # 返回错误信息
            yield "Error: Paper not found."
            return
        
        paper_info = paper.to_dict()
        
        if not rag_service.get_or_create_rag_context(paper_id, paper_info):
            # 返回错误信息
            print(f"为论文 {paper_id} 获取或创建RAG上下文失败")
            yield f"Error: Failed to create RAG context for paper {paper_id}."
            return

        # 1. 检索相关文档
        last_user_message = messages[-1]['content']
        context_docs = rag_service.search_relevant_documents(paper_id, last_user_message)
        
        # 2. 构建RAG提示词
        rag_prompt = rag_service.create_rag_prompt(last_user_message, context_docs, paper_info)
        
        # 构建RAG对话消息
        rag_messages = []
        
        # 添加历史对话（除了最后一条用户消息）
        for msg in messages[:-1]:
            if msg.get('role') and msg.get('content'):
                rag_messages.append(msg)
        
        # 构建当前用户消息
        current_user_content = [{"type": "text", "text": rag_prompt}]

        is_first_user_message = len([m for m in messages if m.get('role') == 'user']) == 1
        if is_first_user_message and paper_info and paper_info.get('file_path'):
            pdf_path = paper_info['file_path']
            if os.path.exists(pdf_path):
                base64_pdf = encode_pdf_to_base64(pdf_path)
                if base64_pdf:
                    data_url = f"data:application/pdf;base64,{base64_pdf}"
                    current_user_content.append({
                        "type": "file",
                        "file": {
                            "filename": os.path.basename(pdf_path),
                            "file_data": data_url
                        }
                    })

        # 添加RAG增强的用户消息
        rag_messages.append({
            'role': 'user',
            'content': current_user_content if len(current_user_content) > 1 else current_user_content[0]['text']
        })

        headers = {
            'Authorization': f'Bearer {llm_config.get("api_key", "")}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:5001',
            'X-Title': 'DocuMancer'
        }

        payload = {
            'model': llm_config.get('model', 'google/gemini-2.5-pro'),
            'messages': rag_messages,
            'max_tokens': llm_config.get('max_tokens', 4096),
            'temperature': llm_config.get('temperature', 0.5),
            'stream': True
        }

        if is_first_user_message and isinstance(rag_messages[-1]['content'], list) and any(item.get('type') == 'file' for item in rag_messages[-1]['content']):
            payload['plugins'] = [
                {
                    "id": "file-parser",
                    "pdf": { "engine": "native" }
                }
            ]
        
        # 首先，以特殊格式发送溯源信息
        sources_data = [{
            "text": doc.get("text"),
            "page_num": doc.get("page_num"),
            "bbox": doc.get("bbox")
        } for doc in context_docs if doc.get("page_num") and doc.get("bbox")]
        
        if sources_data:
            sources_json = json.dumps(sources_data)
            yield f"###SOURCES_START###{sources_json}###SOURCES_END###"

        # 3. 发起流式API请求
        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            stream=True,
            timeout=120  
        )

        # ... a lot of existing code ...

        # 检查响应状态码
        if response.status_code == 200:
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith('data: '):
                        json_str = decoded_line[6:]
                        if json_str.strip() == '[DONE]':
                            break
                        try:
                            data = json.loads(json_str)
                            if 'choices' in data and len(data['choices']) > 0:
                                chunk = data['choices'][0]
                                if 'delta' in chunk and 'content' in chunk['delta']:
                                    content = chunk['delta']['content']
                                    if content:
                                        yield content
                        except json.JSONDecodeError:
                            print(f"JSON解码错误: {json_str}")
                            continue
        else:
            print(f"流式API调用失败: {response.status_code}, {response.text}")
            yield f"Error: Failed to get response from LLM. Status: {response.status_code}"

    except Exception as e:
        print(f"流式RAG API调用错误: {e}")
        yield "Error: An unexpected error occurred during the request."

def call_llm_api_with_multi_doc_rag(messages, main_paper_id, reference_paper_ids, main_paper, reference_papers):
    """调用LLM API进行多文档RAG增强的对话"""
    try:
        # 获取用户最新的问题
        user_message = ""
        for msg in reversed(messages):
            if msg.get('role') == 'user':
                user_message = msg.get('content', '')
                break
        
        if not user_message:
            return call_llm_api(messages, main_paper)
        
        is_first_user_message = len([m for m in messages if m.get('role') == 'user']) == 1
        
        # 确保所有论文的RAG上下文存在
        if not rag_service.get_or_create_rag_context(main_paper_id, main_paper):
            print(f"主论文RAG上下文创建失败，回退到普通对话模式")
            return call_llm_api(messages, main_paper)
        
        # 确保参考论文的RAG上下文存在
        for ref_id, ref_paper in zip(reference_paper_ids, reference_papers):
            if not rag_service.get_or_create_rag_context(ref_id, ref_paper):
                print(f"参考论文 {ref_id} RAG上下文创建失败")
        
        # 进行多文档搜索
        relevant_docs = rag_service.search_multi_doc_relevant_documents(
            main_paper_id, 
            reference_paper_ids, 
            user_message, 
            k=8  # 多文档模式下获取更多相关内容
        )
        
        if not relevant_docs:
            print(f"未找到相关文档，回退到普通对话模式")
            return call_llm_api(messages, main_paper)
        
        # 创建多文档RAG增强的提示词
        rag_prompt = rag_service.create_multi_doc_rag_prompt(
            user_message, 
            relevant_docs, 
            main_paper, 
            reference_papers
        )
        
        # 构建RAG对话消息
        rag_messages = []
        
        # 添加历史对话（除了最后一条用户消息）
        for msg in messages[:-1]:
            if msg.get('role') and msg.get('content'):
                rag_messages.append(msg)
        
        # 构建当前用户消息
        current_user_content = [{"type": "text", "text": rag_prompt}]

        # 在多文档对话中，始终包含所有论文的PDF文件
        all_papers = [main_paper] + reference_papers
        for paper in all_papers:
            if paper and paper.get('file_path') and os.path.exists(paper['file_path']):
                base64_pdf = encode_pdf_to_base64(paper['file_path'])
                if base64_pdf:
                    data_url = f"data:application/pdf;base64,{base64_pdf}"
                    current_user_content.append({
                        "type": "file",
                        "file": {
                            "filename": os.path.basename(paper['file_path']),
                            "file_data": data_url
                        }
                    })
        
        # 添加RAG增强的用户消息
        rag_messages.append({
            'role': 'user',
            'content': current_user_content if len(current_user_content) > 1 else current_user_content[0]['text']
        })
        
        # 调用LLM API
        llm_config = CONFIG.get('llm', {})
        
        headers = {
            'Authorization': f'Bearer {llm_config.get("api_key", "")}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:5001',
            'X-Title': 'DocuMancer'
        }

        payload = {
            'model': llm_config.get('model', 'google/gemini-2.5-flash'),
            'messages': rag_messages,
            'max_tokens': llm_config.get('max_tokens', 32768),
            'temperature': llm_config.get('temperature', 0.7),
            'stream': False
        }

        # 在多文档对话中，始终启用文件解析插件
        if isinstance(rag_messages[-1]['content'], list) and any(item.get('type') == 'file' for item in rag_messages[-1]['content']):
            payload['plugins'] = [
                {
                    "id": "file-parser",
                    "pdf": { "engine": "native" }
                }
            ]

        print(f"正在调用多文档RAG增强的LLM API: {llm_config.get('model')}")
        
        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                return result['choices'][0]['message']['content']
            else:
                print(f"多文档RAG API响应格式错误: {result}")
                return "抱歉，AI助手暂时无法回复，请稍后再试。"
        else:
            print(f"多文档RAG API调用失败: {response.status_code}, {response.text}")
            return f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"多文档RAG LLM API调用错误: {e}")
        return "抱歉，AI助手遇到了技术问题，请稍后再试。"

def call_pre_read_api(pdf_path, prompt_content):
    """调用预阅读模型API"""
    try:
        pre_read_config = CONFIG.get('pre_read_model', {})
        
        base64_pdf = encode_pdf_to_base64(pdf_path)
        if not base64_pdf:
            return "抱歉，无法处理PDF文件，请检查文件是否损坏。"
        
        data_url = f"data:application/pdf;base64,{base64_pdf}"
        
        # 构建API请求
        api_messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt_content
                    },
                    {
                        "type": "file",
                        "file": {
                            "filename": os.path.basename(pdf_path),
                            "file_data": data_url
                        }
                    },
                ]
            }
        ]

        headers = {
            'Authorization': f'Bearer {pre_read_config.get("api_key", "")}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:5001',  # OpenRouter要求
            'X-Title': 'DocuMancer'  # OpenRouter要求
        }

        payload = {
            'model': pre_read_config.get('model', 'google/gemini-2.5-pro'),
            'messages': api_messages,
            'max_tokens': pre_read_config.get('max_tokens', 4096),
            'temperature': pre_read_config.get('temperature', 0.3),
            'stream': False,
            'plugins': [
                {
                    "id": "file-parser",
                    "pdf": { "engine": "native" }
                }
            ]
        }

        print(f"正在调用预阅读API: {pre_read_config.get('model')}")
        
        response = requests.post(
            f"{pre_read_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=120  # 预阅读可能需要更长时间
        )

        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                return result['choices'][0]['message']['content']
            else:
                print(f"预阅读API响应格式错误: {result}")
                return "抱歉，预阅读服务暂时无法使用，请稍后再试。"
        else:
            print(f"预阅读API调用失败: {response.status_code}, {response.text}")
            return f"预阅读服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"预阅读API调用错误: {e}")
        return "抱歉，预阅读服务遇到了技术问题，请稍后再试。"

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('data', exist_ok=True)
os.makedirs('pre_reads', exist_ok=True)  # 确保预阅读目录存在
os.makedirs('hidden_pre_reads', exist_ok=True)  # 确保隐藏预阅读目录存在
os.makedirs('vector_store', exist_ok=True)  # 确保向量数据库目录存在

# 论文数据存储（简单的JSON文件存储）
PAPERS_FILE = 'data/papers.json'

def load_papers():
    """加载已保存的论文数据"""
    papers = Paper.query.all()
    return [paper.to_dict() for paper in papers]


def save_papers(papers):
    """保存论文数据"""
    # This function is no longer needed with a database.
    # We will modify the calling code to directly interact with the database.
    pass

def extract_pdf_info(file_path):
    """从PDF文件提取论文信息"""
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            # 提取文本内容
            text = ""
            for page in pdf_reader.pages[:3]:  # 只读前3页
                text += page.extract_text()
            
            # 尝试从PDF元数据获取信息
            metadata = pdf_reader.metadata
            title = metadata.get('/Title', '') if metadata else ''
            author = metadata.get('/Author', '') if metadata else ''
            
            # 如果元数据没有标题，尝试从文本提取
            if not title:
                lines = text.split('\n')
                # 通常标题在前几行，且字体较大
                for line in lines[:10]:
                    line = line.strip()
                    if len(line) > 10 and len(line) < 200:
                        title = line
                        break
            
            # 简单的摘要提取（查找Abstract关键词）
            abstract = ""
            abstract_match = re.search(r'(?i)abstract[:\s\n]+(.*?)(?=\n\s*(?:keywords?|introduction|1\.|references?))', text, re.DOTALL)
            if abstract_match:
                abstract = abstract_match.group(1).strip()[:500]  # 限制长度
            
            return {
                'title': title or 'Unknown Title',
                'authors': author or 'Unknown Author',
                'abstract': abstract or 'No abstract available',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'source': 'PDF Upload',
                'has_pdf': True
            }
    except Exception as e:
        print(f"PDF解析错误: {e}")
        return {
            'title': 'PDF Parse Error',
            'authors': 'Unknown',
            'abstract': 'Failed to extract information from PDF',
            'date': datetime.now().strftime('%Y-%m-%d'),
            'source': 'PDF Upload',
            'has_pdf': False
        }

def download_arxiv_pdf(arxiv_id):
    """从arXiv下载PDF文件"""
    try:
        # 清理arXiv ID
        clean_id = arxiv_id.replace('https://arxiv.org/abs/', '').replace('http://arxiv.org/abs/', '')
        
        # arXiv PDF URL
        pdf_url = f"http://arxiv.org/pdf/{clean_id}.pdf"
        
        # 生成本地文件名
        filename = f"arxiv_{clean_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        
        # 下载PDF文件
        print(f"正在下载arXiv PDF: {pdf_url}")
        response = requests.get(pdf_url, timeout=30, stream=True)
        
        if response.status_code == 200:
            # 检查响应内容类型
            content_type = response.headers.get('content-type', '')
            if 'application/pdf' in content_type or 'application/octet-stream' in content_type:
                # 保存文件
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                print(f"arXiv PDF下载成功: {file_path}")
                return file_path
            else:
                print(f"下载的文件不是PDF格式: {content_type}")
                return None
        else:
            print(f"arXiv PDF下载失败: HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"arXiv PDF下载错误: {e}")
        return None

def fetch_arxiv_paper(arxiv_id):
    """从arXiv获取论文信息并下载PDF"""
    try:
        # 清理arXiv ID
        arxiv_id = arxiv_id.replace('https://arxiv.org/abs/', '').replace('http://arxiv.org/abs/', '')
        
        # 调用arXiv API获取论文信息
        url = f"http://export.arxiv.org/api/query?id_list={arxiv_id}"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            # 解析XML响应
            root = ET.fromstring(response.content)
            entry = root.find('{http://www.w3.org/2005/Atom}entry')
            
            if entry is not None:
                title = entry.find('{http://www.w3.org/2005/Atom}title').text.strip()
                
                # 获取作者
                authors = []
                for author in entry.findall('{http://www.w3.org/2005/Atom}author'):
                    name = author.find('{http://www.w3.org/2005/Atom}name').text
                    authors.append(name)
                
                # 获取摘要
                abstract = entry.find('{http://www.w3.org/2005/Atom}summary').text.strip()
                
                # 获取发布日期
                published = entry.find('{http://www.w3.org/2005/Atom}published').text
                date = published.split('T')[0]  # 提取日期部分
                
                # 下载PDF文件
                pdf_path = download_arxiv_pdf(arxiv_id)
                
                paper_info = {
                    'title': title,
                    'authors': ', '.join(authors),
                    'abstract': abstract,
                    'date': date,
                    'source': f'arXiv:{arxiv_id}',
                    'arxiv_id': arxiv_id
                }
                
                # 如果PDF下载成功，添加文件路径
                if pdf_path:
                    paper_info['file_path'] = pdf_path
                    paper_info['has_pdf'] = True
                else:
                    paper_info['has_pdf'] = False
                    print(f"警告：arXiv论文 {arxiv_id} 的PDF下载失败，但论文信息已获取")
                
                return paper_info
                
    except Exception as e:
        print(f"arXiv获取错误: {e}")
        return None

def calculate_similarity(str1, str2):
    """计算两个字符串的相似度"""
    if not str1 or not str2:
        return 0.0
    return SequenceMatcher(None, str1.lower(), str2.lower()).ratio()

def normalize_title(title):
    """标准化标题，移除特殊字符和多余空格"""
    if not title:
        return ""
    # 移除特殊字符，保留字母、数字和空格
    normalized = re.sub(r'[^\w\s]', ' ', title)
    # 移除多余空格
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    return normalized.lower()

def normalize_authors(authors):
    """标准化作者信息"""
    if not authors:
        return ""
    # 移除多余空格和标点
    normalized = re.sub(r'[^\w\s,]', ' ', authors)
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    return normalized.lower()

def is_duplicate_paper(new_paper, existing_papers, similarity_threshold=0.8):
    """
    检查新论文是否与现有论文重复
    
    Args:
        new_paper: 新论文信息
        existing_papers: 现有论文列表
        similarity_threshold: 相似度阈值
    
    Returns:
        tuple: (是否重复, 重复的论文信息)
    """
    new_title = normalize_title(new_paper.get('title', ''))
    new_authors = normalize_authors(new_paper.get('authors', ''))
    new_arxiv_id = new_paper.get('arxiv_id', '').strip()
    
    for existing_paper in existing_papers:
        if not existing_paper:
            continue
            
        existing_title = normalize_title(existing_paper.get('title', ''))
        existing_authors = normalize_authors(existing_paper.get('authors', ''))
        existing_arxiv_id = (existing_paper.get('arxiv_id') or '').strip()
        
        # 1. arXiv ID 精确匹配
        if new_arxiv_id and existing_arxiv_id and new_arxiv_id == existing_arxiv_id:
            return True, existing_paper
        
        # 2. 标题相似度检测
        title_similarity = calculate_similarity(new_title, existing_title)
        if title_similarity >= similarity_threshold:
            # 如果标题非常相似，进一步检查作者
            authors_similarity = calculate_similarity(new_authors, existing_authors)
            if authors_similarity >= 0.7:  # 作者相似度阈值
                return True, existing_paper
        
        # 3. 文件内容哈希匹配（如果有PDF文件）
        if (new_paper.get('file_path') and existing_paper.get('file_path') and
            os.path.exists(new_paper['file_path']) and os.path.exists(existing_paper['file_path'])):
            try:
                with open(new_paper['file_path'], 'rb') as f1, open(existing_paper['file_path'], 'rb') as f2:
                    if f1.read() == f2.read():
                        return True, existing_paper
            except Exception as e:
                print(f"文件比较错误: {e}")
    
    return False, None

def remove_duplicate_papers(papers):
    """
    移除重复的论文。
    使用智能去重逻辑，包括标题相似度、作者匹配和文件哈希。
    """
    if not papers:
        return []
    
    unique_papers = [papers[0]]  # 保留第一篇论文
    
    for i in range(1, len(papers)):
        current_paper = papers[i]
        is_duplicate = False
        
        # 检查是否与已保留的论文重复
        for existing_paper in unique_papers:
            duplicate, _ = is_duplicate_paper(current_paper, [existing_paper])
            if duplicate:
                is_duplicate = True
                print(f"移除重复论文: {current_paper.get('title', '未知')}")
                break
        
        if not is_duplicate:
            unique_papers.append(current_paper)
    
    return unique_papers

def extract_text_from_pdf(file_path):
    """从PDF文件中提取纯文本内容"""
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text()
            return text
    except Exception as e:
        print(f"从PDF提取文本时出错: {e}")
        return None

def verify_arxiv_id(original_paper_id, candidate_arxiv_id, similarity_threshold=0.7):
    """
    验证给定的ArXiv ID是否与原始论文匹配
    
    Args:
        original_paper_id: 数据库中原始论文的ID
        candidate_arxiv_id: 从LLM获取的候选ArXiv ID
        similarity_threshold: 标题相似度阈值

    Returns:
        bool: 如果验证成功则返回True，否则返回False
    """
    print(f"正在验证 ArXiv ID: {candidate_arxiv_id} (原始论文 ID: {original_paper_id})...")
    
    # 1. 获取原始论文信息
    original_paper = db.session.get(Paper, original_paper_id)
    if not original_paper:
        print(f"错误: 找不到ID为 {original_paper_id} 的原始论文")
        return False

    # 2. 从ArXiv下载候选论文的PDF
    temp_pdf_path = download_arxiv_pdf(candidate_arxiv_id)
    if not temp_pdf_path:
        print(f"验证失败: 无法下载ArXiv ID为 {candidate_arxiv_id} 的PDF")
        return False

    try:
        # 3. 提取两篇论文的标题进行比较
        # 原始论文的标题
        original_title = normalize_title(original_paper.title)

        # 候选论文的标题
        candidate_info = extract_pdf_info(temp_pdf_path)
        candidate_title = normalize_title(candidate_info.get('title', ''))

        # 4. 计算标题相似度
        title_similarity = calculate_similarity(original_title, candidate_title)
        
        print(f"  - 原始论文标题: '{original_title}'")
        print(f"  - 候选论文标题: '{candidate_title}'")
        print(f"  - 标题相似度: {title_similarity:.2f}")

        # 5. 判断是否匹配
        if title_similarity >= similarity_threshold:
            print(f"  => 验证成功: ArXiv ID {candidate_arxiv_id} 匹配。")
            return True
        else:
            print(f"  => 验证失败: ArXiv ID {candidate_arxiv_id} 不匹配。")
            return False

    finally:
        # 6. 清理下载的临时文件
        if os.path.exists(temp_pdf_path):
            try:
                os.remove(temp_pdf_path)
                print(f"已清理临时文件: {temp_pdf_path}")
            except Exception as e:
                print(f"清理临时文件失败: {e}")


def call_llm_api_for_arxiv_id(paper_title, paper_authors, retries=3, previous_attempts=None):
    """
    专门用于获取ArXiv ID的LLM调用，包含重试和修正机制
    
    Args:
        paper_title: 论文标题
        paper_authors: 论文作者
        retries: 最大重试次数
        previous_attempts: 先前尝试过的错误ID列表

    Returns:
        str: ArXiv ID, 'NOT_FOUND', or None
    """
    if previous_attempts is None:
        previous_attempts = []

    try:
        llm_config = CONFIG.get('llm', {})
        
        # 构建系统提示
        system_prompt = f"""You are a helpful assistant that finds arXiv IDs for academic papers.
        Based on the paper title, authors, and the provided web search results, find and return the arXiv ID.
        If the paper is on arXiv, return ONLY the arXiv ID (e.g., '2301.07041').
        If you are certain the paper is not on arXiv, return the exact string 'NOT_FOUND'.
        Do not provide any extra explanation or text."""

        if previous_attempts:
            system_prompt += f"""

IMPORTANT: You have previously tried the following arXiv IDs and they were incorrect: {', '.join(previous_attempts)}.
Please find a different and correct arXiv ID. Do not repeat these past mistakes."""

        user_prompt = f"""Paper Title: "{paper_title}"
        Authors: <AUTHORS>
        """

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        headers = {
            'Authorization': f'Bearer {llm_config.get("api_key", "")}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:5001',
            'X-Title': 'DocuMancer'
        }

        payload = {
            'model': 'google/gemini-2.5-flash:online',
            'messages': messages,
            'max_tokens': 50,
            'temperature': 0.3,
            'stream': False,
            'plugins': [
                {
                    "id": "web",
                    "max_results": 3,
                    "search_prompt": "Based on the following web search results, find the arXiv ID for the paper."
                }
            ]
        }
        
        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content'].strip()
                # 验证返回的ID格式
                if re.match(r'^\d{4}\.\d{4,5}$', content) or content == 'NOT_FOUND':
                    return content
                else:
                    return None # 返回格式不符
            else:
                return None
        elif response.status_code == 429:
            retry_after = int(response.headers.get("Retry-After", 10))
            print(f"ArXiv ID LLM call rate limited. Retrying after {retry_after} seconds...")
            time.sleep(retry_after)
            return call_llm_api_for_arxiv_id(paper_title, paper_authors, retries - 1, previous_attempts)
        else:
            print(f"ArXiv ID LLM call failed: {response.status_code}, {response.text}")
            return None

    except Exception as e:
        print(f"Error calling LLM for ArXiv ID: {e}")
        return None

def find_missing_arxiv_ids_task():
    """在后台查找并更新缺失的ArXiv ID"""
    with app.app_context():
        # 等待应用完全启动
        time.sleep(10)
        
        print("开始检查缺失的ArXiv ID...")
        papers_to_check = Paper.query.filter(Paper.arxiv_id.is_(None) | (Paper.arxiv_id == '')).all()
        
        for paper in papers_to_check:
            print(f"正在为论文 '{paper.title}' 查找ArXiv ID...")
            
            attempts = 0
            max_attempts = 3
            verified = False
            incorrect_ids = []

            while attempts < max_attempts and not verified:
                attempts += 1
                print(f"  - 尝试次数: {attempts}/{max_attempts}")

                arxiv_id = call_llm_api_for_arxiv_id(paper.title, paper.authors, previous_attempts=incorrect_ids)
                
                if arxiv_id and arxiv_id != 'NOT_FOUND':
                    if arxiv_id in incorrect_ids:
                        print(f"  - LLM返回了已知的错误ID '{arxiv_id}'，跳过验证。")
                        continue

                    # 验证ArXiv ID
                    if verify_arxiv_id(paper.id, arxiv_id):
                        print(f"为论文 '{paper.title}' 找到并验证了ArXiv ID: {arxiv_id}")
                        paper.arxiv_id = arxiv_id
                        db.session.commit()
                        verified = True
                    else:
                        print(f"为论文 '{paper.title}' 找到的ArXiv ID '{arxiv_id}' 未通过验证。")
                        incorrect_ids.append(arxiv_id)
                        if attempts == max_attempts:
                           #不再标记为 'VERIFICATION_FAILED'
                           # paper.arxiv_id = 'VERIFICATION_FAILED'
                           # db.session.commit()
                           print(f"  - 已达到最大尝试次数，将论文的ArXiv ID保留为空。")

                elif arxiv_id == 'NOT_FOUND':
                    print(f"论文 '{paper.title}' 未在ArXiv上找到，其ID将保留为空。")
                    # 不再标记为 'NOT_FOUND'
                    # paper.arxiv_id = 'NOT_FOUND'
                    # db.session.commit()
                    break 
                else:
                    print(f"无法为论文 '{paper.title}' 找到ArXiv ID。")
                    if attempts == max_attempts:
                        print(f"  - 已达到最大尝试次数，未找到ArXiv ID。")
                
                if not verified and attempts < max_attempts:
                    time.sleep(2) # 短暂休眠后重试

            # 避免过于频繁的API调用
            time.sleep(5) 
            
        print("ArXiv ID检查完成。")

@app.route('/')
def index():
    """论文库主页"""
    papers = load_papers()
    return render_template('index.html', papers=papers)

@app.route('/upload_pdf', methods=['POST'])
def upload_pdf():
    """处理PDF上传"""
    if 'pdf_file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400
    
    file = request.files['pdf_file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and file.filename.lower().endswith('.pdf'):
        # 保存文件
        filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(file_path)
        
        # 解析PDF信息
        paper_info = extract_pdf_info(file_path)

        # 检查PDF解析是否成功
        if paper_info.get('title') == 'PDF Parse Error':
            # 删除已上传的无效文件
            try:
                os.remove(file_path)
                print(f"PDF解析失败，已删除文件: {file_path}")
            except Exception as e:
                print(f"删除无效文件失败: {e}")
            
            return jsonify({
                'success': False,
                'error': 'PDF文件解析失败，请检查文件是否损坏或格式是否正确。'
            }), 400
        
        paper_info['file_path'] = file_path
        
        # 检查是否重复
        papers = load_papers()
        is_duplicate, duplicate_paper = is_duplicate_paper(paper_info, papers)
        
        if is_duplicate:
            # 删除新上传的文件
            try:
                os.remove(file_path)
                print(f"检测到重复论文，已删除新上传的文件: {file_path}")
            except Exception as e:
                print(f"删除重复文件失败: {e}")
            
            return jsonify({
                'success': False,
                'error': '检测到重复论文',
                'duplicate_paper': {
                    'id': duplicate_paper['id'],
                    'title': duplicate_paper['title'],
                    'authors': duplicate_paper['authors'],
                    'source': duplicate_paper['source']
                },
                'message': f'论文 "{duplicate_paper["title"]}" 已存在于论文库中'
            }), 409  # 409 Conflict
        
        # 保存到数据库
        new_paper = Paper(
            title=paper_info.get('title'),
            authors=paper_info.get('authors'),
            abstract=paper_info.get('abstract'),
            date=paper_info.get('date'),
            source=paper_info.get('source'),
            file_path=paper_info.get('file_path'),
            has_pdf=paper_info.get('has_pdf')
        )
        db.session.add(new_paper)
        db.session.commit()

        # 创建关联的PaperHistory
        new_history = PaperHistory(paper_id=new_paper.id)
        db.session.add(new_history)
        db.session.commit()
        
        return jsonify({'success': True, 'paper': new_paper.to_dict()})
    
    return jsonify({'error': 'Invalid file type'}), 400

@app.route('/add_arxiv', methods=['POST'])
def add_arxiv():
    """添加arXiv论文"""
    data = request.get_json()
    arxiv_id = data.get('arxiv_id', '').strip()
    
    if not arxiv_id:
        return jsonify({'error': 'ArXiv ID required'}), 400
    
    paper_info = fetch_arxiv_paper(arxiv_id)
    if paper_info:
        # 检查是否重复
        papers = load_papers()
        is_duplicate, duplicate_paper = is_duplicate_paper(paper_info, papers)
        
        if is_duplicate:
            # 删除新下载的文件（如果存在）
            if paper_info.get('file_path') and os.path.exists(paper_info['file_path']):
                try:
                    os.remove(paper_info['file_path'])
                    print(f"检测到重复论文，已删除新下载的文件: {paper_info['file_path']}")
                except Exception as e:
                    print(f"删除重复文件失败: {e}")
            
            return jsonify({
                'success': False,
                'error': '检测到重复论文',
                'duplicate_paper': {
                    'id': duplicate_paper['id'],
                    'title': duplicate_paper['title'],
                    'authors': duplicate_paper['authors'],
                    'source': duplicate_paper['source']
                },
                'message': f'论文 "{duplicate_paper["title"]}" 已存在于论文库中'
            }), 409  # 409 Conflict
        
        # 保存到数据库
        new_paper = Paper(
            title=paper_info.get('title'),
            authors=paper_info.get('authors'),
            abstract=paper_info.get('abstract'),
            date=paper_info.get('date'),
            source=paper_info.get('source'),
            file_path=paper_info.get('file_path'),
            arxiv_id=paper_info.get('arxiv_id'),
            has_pdf=paper_info.get('has_pdf')
        )
        db.session.add(new_paper)
        db.session.commit()

        # 创建关联的PaperHistory
        new_history = PaperHistory(paper_id=new_paper.id)
        db.session.add(new_history)
        db.session.commit()
        
        return jsonify({'success': True, 'paper': new_paper.to_dict()})
    else:
        return jsonify({'error': 'Failed to fetch arXiv paper'}), 400

@app.route('/delete_paper/<int:paper_id>', methods=['DELETE'])
def delete_paper(paper_id):
    """删除论文"""
    try:
        # 查找要删除的论文
        paper_to_delete = db.session.get(Paper, paper_id)
        
        if not paper_to_delete:
            return jsonify({'error': '论文不存在'}), 404
        
        # 删除PDF文件（如果存在）
        if paper_to_delete.file_path and os.path.exists(paper_to_delete.file_path):
            try:
                os.remove(paper_to_delete.file_path)
                print(f"已删除PDF文件: {paper_to_delete.file_path}")
            except Exception as e:
                print(f"删除PDF文件失败: {e}")
                # 即使文件删除失败，也继续删除数据库记录
        
        deleted_paper_info = paper_to_delete.to_dict()
        
        # 从数据库中删除
        db.session.delete(paper_to_delete)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '论文删除成功',
            'deleted_paper': {
                'id': deleted_paper_info['id'],
                'title': deleted_paper_info['title']
            }
        })
        
    except Exception as e:
        print(f"删除论文错误: {e}")
        return jsonify({
            'error': '删除论文时发生错误，请稍后重试',
            'success': False
        }), 500

@app.route('/chat/<int:paper_id>')
def chat_with_paper(paper_id):
    """与特定论文聊天"""
    paper = db.session.get(Paper, paper_id)
    
    if not paper:
        return redirect(url_for('index'))
    
    return render_template('chat.html', paper=paper.to_dict())

@app.route('/view_pdf/<int:paper_id>')
def view_pdf(paper_id):
    """查看论文PDF文件"""
    paper = db.session.get(Paper, paper_id)
    
    if not paper or not paper.file_path:
        return "PDF文件不存在", 404
    
    try:
        return send_file(paper.file_path, mimetype='application/pdf')
    except Exception as e:
        print(f"PDF文件读取错误: {e}")
        return "PDF文件读取失败", 500

@app.route('/chat_api', methods=['POST'])
def chat_api():
    """聊天API - 与论文进行智能对话"""
    data = request.get_json()
    message = data.get('message', '').strip()
    paper_id = data.get('paper_id')
    
    if not message:
        return jsonify({'error': '消息不能为空'}), 400
    
    if not paper_id:
        return jsonify({'error': '缺少论文ID'}), 400
    
    try:
        # 获取论文和其历史记录
        paper = db.session.get(Paper, paper_id)
        
        if not paper:
            return jsonify({'error': '论文不存在'}), 404
        
        # 确保PaperHistory存在
        if not paper.history:
            paper.history = PaperHistory(paper_id=paper.id)
            db.session.commit()

        # 构建对话消息列表
        messages = []
        
        # 添加历史对话
        # 只保留最近10轮对话 (user + assistant = 1 round)
        dialogue_history = DialogueMessage.query.filter_by(history_id=paper.history.id).order_by(DialogueMessage.timestamp.desc()).limit(20).all()
        dialogue_history.reverse() # 恢复时间顺序

        for history_item in dialogue_history:
            messages.append({
                'role': history_item.role,
                'content': history_item.content
            })
        
        # 添加当前用户消息
        messages.append({
            'role': 'user',
            'content': message
        })

        # 保存用户消息到数据库
        user_message = DialogueMessage(history_id=paper.history.id, role='user', content=message)
        db.session.add(user_message)
        db.session.commit()
        
        # 调用RAG增强的LLM API
        response_text = call_llm_api_with_rag(messages, paper_id, paper_context=paper.to_dict())
        
        # 保存助手消息到数据库
        assistant_message = DialogueMessage(history_id=paper.history.id, role='assistant', content=response_text)
        db.session.add(assistant_message)
        db.session.commit()

        return jsonify({
            'success': True,
            'response': response_text,
            'paper_title': paper.title,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        print(f"聊天API错误: {e}")
        return jsonify({
            'error': '服务器内部错误，请稍后重试',
            'success': False
        }), 500

@app.route('/chat_stream', methods=['POST'])
def chat_stream():
    """处理与单篇论文的流式聊天"""
    data = request.get_json()
    paper_id = data.get('paper_id')
    chat_history = data.get('chat_history', []) # 前端发送的完整历史

    if not paper_id or not chat_history:
        return Response(json.dumps({'error': 'Missing paper_id or chat_history'}), status=400, mimetype='application/json')

    try:
        paper = db.session.get(Paper, paper_id)
        if not paper:
            return Response(json.dumps({'error': 'Paper not found'}), status=404, mimetype='application/json')

        if not paper.history:
            paper.history = PaperHistory(paper_id=paper.id)
            db.session.commit()

        # 从前端历史中获取最新的用户消息
        user_message_content = chat_history[-1]['content']

        # 保存用户消息
        user_message = DialogueMessage(history_id=paper.history.id, role='user', content=user_message_content)
        db.session.add(user_message)
        db.session.commit()

        # 构建发送给LLM的messages (可以包含更多历史)
        messages_for_llm = [{'role': msg['role'], 'content': msg['content']} for msg in chat_history]

        def generate():
            """包装生成器以发送正确的SSE格式并保存完整响应"""
            full_response = []
            generator = call_llm_api_stream_with_rag(messages_for_llm, paper_id)
            try:
                # 首先处理可能存在的溯源信息
                first_chunk = next(generator)
                if first_chunk.startswith("###SOURCES_START###"):
                    parts = first_chunk.split("###SOURCES_START###")[1].split("###SOURCES_END###")
                    sources_json_str = parts[0]
                    try:
                        sources_data = json.loads(sources_json_str)
                        yield f"data: {json.dumps({'type': 'sources', 'data': sources_data})}\n\n"
                    except json.JSONDecodeError:
                        print(f"Error decoding sources JSON: {sources_json_str}")

                    remaining_content = parts[1]
                    if remaining_content:
                        full_response.append(remaining_content)
                        yield f"data: {json.dumps({'type': 'content', 'content': remaining_content})}\n\n"
                else:
                    full_response.append(first_chunk)
                    yield f"data: {json.dumps({'type': 'content', 'content': first_chunk})}\n\n"

                # 处理剩余的流式内容
                for chunk in generator:
                    full_response.append(chunk)
                    yield f"data: {json.dumps({'type': 'content', 'content': chunk})}\n\n"
                    
            except StopIteration:
                pass # 生成器正常结束
            except Exception as e:
                print(f"流式包装器错误: {e}")
                yield f"data: {json.dumps({'type': 'error', 'content': '流处理过程中发生错误。'})}\n\n"
            finally:
                # 保存完整的助手响应
                final_content = "".join(full_response)
                if final_content:
                    with app.app_context():
                        assistant_message = DialogueMessage(history_id=paper.history.id, role='assistant', content=final_content)
                        db.session.add(assistant_message)
                        db.session.commit()
                yield f"data: {json.dumps({'type': 'done'})}\n\n"

        return Response(stream_with_context(generate()), mimetype='text/event-stream')

    except Exception as e:
        print(f"Stream chat error: {e}")
        return Response(json.dumps({'error': 'Internal server error'}), status=500, mimetype='application/json')

@app.route('/api/get_chat_history/<int:paper_id>', methods=['GET'])
def get_chat_history(paper_id):
    """获取指定论文的聊天记录"""
    paper = db.session.get(Paper, paper_id)
    if not paper:
        return jsonify({'error': 'Paper not found'}), 404

    if not paper.history:
        return jsonify({'history': []})

    messages = DialogueMessage.query.filter_by(history_id=paper.history.id).order_by(DialogueMessage.timestamp.asc()).all()
    
    history_list = [msg.to_dict() for msg in messages]
    
    return jsonify({'history': history_list})

@app.route('/generate_pre_read/<int:paper_id>', methods=['POST'])
def generate_pre_read(paper_id):
    """生成论文预阅读"""
    try:
        # 获取论文信息
        paper = db.session.get(Paper, paper_id)
        
        if not paper:
            return jsonify({'error': '论文不存在'}), 404
        
        # 检查是否有PDF文件
        if not paper.file_path or not os.path.exists(paper.file_path):
            return jsonify({'error': '论文PDF文件不存在'}), 404
        
        # 检查是否已经有预阅读文件
        pre_read_filename = f"pre_read_{paper_id}.md"
        pre_read_path = os.path.join('pre_reads', pre_read_filename)
        
        if os.path.exists(pre_read_path):
            return jsonify({'success': True, 'message': '预阅读已存在', 'exists': True})
        
        # 读取预阅读提示词
        try:
            with open('static/pre_reads_prompt.md', 'r', encoding='utf-8') as f:
                prompt_content = f.read()
        except Exception as e:
            print(f"提示词文件读取错误: {e}")
            return jsonify({'error': '预阅读提示词文件读取失败'}), 500
        
        # 调用预阅读API
        pre_read_result = call_pre_read_api(paper.file_path, prompt_content)
        
        # 保存预阅读结果
        try:
            with open(pre_read_path, 'w', encoding='utf-8') as f:
                f.write(pre_read_result)
        except Exception as e:
            print(f"预阅读文件保存错误: {e}")
            return jsonify({'error': '预阅读结果保存失败'}), 500
        
        # 删除隐藏预阅读（如果存在），这将触发向量数据库的重建
        rag_service.delete_hidden_pre_read(paper_id)
        
        return jsonify({
            'success': True, 
            'message': '预阅读生成成功',
            'pre_read_path': pre_read_filename
        })
        
    except Exception as e:
        print(f"预阅读生成错误: {e}")
        return jsonify({
            'error': '预阅读生成时发生错误，请稍后重试',
            'success': False
        }), 500

@app.route('/view_pre_read/<int:paper_id>')
def view_pre_read(paper_id):
    """查看论文预阅读"""
    try:
        # 获取论文信息
        paper = db.session.get(Paper, paper_id)
        
        if not paper:
            return redirect(url_for('index'))
        
        # 检查预阅读文件是否存在
        pre_read_filename = f"pre_read_{paper_id}.md"
        pre_read_path = os.path.join('pre_reads', pre_read_filename)
        
        pre_read_content = ""
        pre_read_exists = False
        
        if os.path.exists(pre_read_path):
            try:
                with open(pre_read_path, 'r', encoding='utf-8') as f:
                    pre_read_content = f.read()
                pre_read_exists = True
            except Exception as e:
                print(f"预阅读文件读取错误: {e}")
                pre_read_content = "预阅读文件读取失败"
        
        return render_template('pre_read.html', 
                             paper=paper.to_dict(), 
                             pre_read_content=pre_read_content,
                             pre_read_exists=pre_read_exists)
        
    except Exception as e:
        print(f"预阅读查看错误: {e}")
        return redirect(url_for('index'))

@app.route('/api/papers', methods=['GET'])
def api_get_papers():
    """获取论文列表API"""
    try:
        papers = load_papers()
        
        # 为每篇论文添加预阅读状态信息
        for paper_dict in papers:
            # 检查是否有正式预阅读
            formal_pre_read_path = os.path.join('pre_reads', f"pre_read_{paper_dict['id']}.md")
            paper_dict['has_formal_preread'] = os.path.exists(formal_pre_read_path)
            
            # 检查是否有隐藏预阅读
            hidden_pre_read_path = os.path.join('hidden_pre_reads', f"hidden_pre_read_{paper_dict['id']}.md")
            paper_dict['has_hidden_preread'] = os.path.exists(hidden_pre_read_path)
        
        return jsonify({
            'success': True,
            'papers': papers
        })
        
    except Exception as e:
        print(f"获取论文列表错误: {e}")
        return jsonify({'error': '获取论文列表失败'}), 500

@app.route('/api/start_multi_doc_analysis', methods=['POST'])
def api_start_multi_doc_analysis():
    """初始化跨文档分析API"""
    try:
        data = request.get_json()
        main_paper_id = data.get('main_paper_id')
        reference_paper_ids = data.get('reference_paper_ids', [])
        
        if not main_paper_id:
            return jsonify({'error': '缺少主论文ID'}), 400
        
        if not reference_paper_ids:
            return jsonify({'error': '请至少选择一篇参考论文'}), 400
        
        # 验证论文是否存在
        papers = load_papers()
        main_paper = next((p for p in papers if p['id'] == main_paper_id), None)
        if not main_paper:
            return jsonify({'error': '主论文不存在'}), 404
        
        reference_papers = []
        for ref_id in reference_paper_ids:
            ref_paper = next((p for p in papers if p['id'] == ref_id), None)
            if ref_paper:
                reference_papers.append(ref_paper)
            else:
                return jsonify({'error': f'参考论文ID {ref_id} 不存在'}), 404
        
        # 确保所有参考论文都有预阅读（隐藏或正式）
        processed_papers = []
        for ref_paper in reference_papers:
            # 调用RAG服务确保预阅读存在
            if rag_service.get_or_create_rag_context(ref_paper['id'], ref_paper):
                processed_papers.append(ref_paper['id'])
            else:
                return jsonify({'error': f'论文 {ref_paper["title"]} 的预阅读生成失败'}), 500
        
        # 同样确保主论文有预阅读
        if not rag_service.get_or_create_rag_context(main_paper_id, main_paper):
            return jsonify({'error': '主论文的预阅读生成失败'}), 500
        
        return jsonify({
            'success': True,
            'main_paper_id': main_paper_id,
            'reference_paper_ids': processed_papers,
            'message': f'跨文档分析初始化成功，包含 {len(processed_papers) + 1} 篇论文'
        })
        
    except Exception as e:
        print(f"跨文档分析初始化错误: {e}")
        return jsonify({'error': '初始化失败，请稍后重试'}), 500

@app.route('/chat_multi_doc_api', methods=['POST'])
def chat_multi_doc_api():
    """跨文档分析聊天API"""
    data = request.get_json()
    message = data.get('message', '').strip()
    main_paper_id = data.get('main_paper_id')
    reference_paper_ids = data.get('reference_paper_ids', [])
    chat_history = data.get('chat_history', [])
    
    if not message:
        return jsonify({'error': '消息不能为空'}), 400
    
    if not main_paper_id:
        return jsonify({'error': '缺少主论文ID'}), 400
    
    try:
        # 获取论文信息
        papers = load_papers()
        main_paper = next((p for p in papers if p['id'] == main_paper_id), None)
        
        if not main_paper:
            return jsonify({'error': '主论文不存在'}), 404
        
        # 获取参考论文信息
        reference_papers = []
        for ref_id in reference_paper_ids:
            ref_paper = next((p for p in papers if p['id'] == ref_id), None)
            if ref_paper:
                reference_papers.append(ref_paper)
        
        # 构建对话消息列表
        messages = []
        
        # 添加历史对话
        for history_item in chat_history[-10:]:
            if history_item.get('role') and history_item.get('content'):
                messages.append({
                    'role': history_item['role'],
                    'content': history_item['content']
                })
        
        # 添加当前用户消息
        messages.append({
            'role': 'user',
            'content': message
        })
        
        # 调用多文档RAG增强的LLM API
        response_text = call_llm_api_with_multi_doc_rag(
            messages, 
            main_paper_id, 
            reference_paper_ids,
            main_paper, 
            reference_papers
        )
        
        return jsonify({
            'success': True,
            'response': response_text,
            'main_paper_title': main_paper.get('title', ''),
            'reference_papers_count': len(reference_papers),
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        print(f"跨文档聊天API错误: {e}")
        return jsonify({
            'error': '服务器内部错误，请稍后重试',
            'success': False
        }), 500

def call_llm_api_stream_with_multi_doc_rag(messages, main_paper_id, reference_paper_ids, main_paper, reference_papers):
    """调用LLM API进行多文档RAG增强的流式对话"""
    try:
        # 获取用户最新的问题
        user_message = ""
        for msg in reversed(messages):
            if msg.get('role') == 'user':
                user_message = msg.get('content', '')
                break
        
        if not user_message:
            for chunk in call_llm_api_stream(messages, main_paper):
                yield chunk
            return
        
        is_first_user_message = len([m for m in messages if m.get('role') == 'user']) == 1
        
        # 确保所有论文的RAG上下文存在
        if not rag_service.get_or_create_rag_context(main_paper_id, main_paper):
            print(f"主论文RAG上下文创建失败，回退到普通对话模式")
            for chunk in call_llm_api_stream(messages, main_paper):
                yield chunk
            return
        
        # 确保参考论文的RAG上下文存在
        for ref_id, ref_paper in zip(reference_paper_ids, reference_papers):
            if not rag_service.get_or_create_rag_context(ref_id, ref_paper):
                print(f"参考论文 {ref_id} RAG上下文创建失败")
        
        # 进行多文档搜索
        relevant_docs = rag_service.search_multi_doc_relevant_documents(
            main_paper_id, 
            reference_paper_ids, 
            user_message, 
            k=8  # 多文档模式下获取更多相关内容
        )
        
        if not relevant_docs:
            print(f"未找到相关文档，回退到普通对话模式")
            for chunk in call_llm_api_stream(messages, main_paper):
                yield chunk
            return
        
        # 创建多文档RAG增强的提示词
        rag_prompt = rag_service.create_multi_doc_rag_prompt(
            user_message, 
            relevant_docs, 
            main_paper, 
            reference_papers
        )
        
        # 构建RAG对话消息
        rag_messages = []
        
        # 添加历史对话（除了最后一条用户消息）
        for msg in messages[:-1]:
            if msg.get('role') and msg.get('content'):
                rag_messages.append(msg)
        
        # 构建当前用户消息
        current_user_content = [{"type": "text", "text": rag_prompt}]

        # 在多文档对话中，始终包含所有论文的PDF文件
        all_papers = [main_paper] + reference_papers
        for paper in all_papers:
            if paper and paper.get('file_path') and os.path.exists(paper['file_path']):
                base64_pdf = encode_pdf_to_base64(paper['file_path'])
                if base64_pdf:
                    data_url = f"data:application/pdf;base64,{base64_pdf}"
                    current_user_content.append({
                        "type": "file",
                        "file": {
                            "filename": os.path.basename(paper['file_path']),
                            "file_data": data_url
                        }
                    })
        
        # 添加RAG增强的用户消息
        rag_messages.append({
            'role': 'user',
            'content': current_user_content if len(current_user_content) > 1 else current_user_content[0]['text']
        })
        
        # 调用流式LLM API
        llm_config = CONFIG.get('llm', {})
        
        headers = {
            'Authorization': f'Bearer {llm_config.get("api_key", "")}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:5001',
            'X-Title': 'DocuMancer'
        }

        payload = {
            'model': llm_config.get('model', 'google/gemini-2.5-flash'),
            'messages': rag_messages,
            'max_tokens': llm_config.get('max_tokens', 32768),
            'temperature': llm_config.get('temperature', 0.7),
            'stream': True
        }

        # 在多文档对话中，始终启用文件解析插件
        if isinstance(rag_messages[-1]['content'], list) and any(item.get('type') == 'file' for item in rag_messages[-1]['content']):
            payload['plugins'] = [
                {
                    "id": "file-parser",
                    "pdf": { "engine": "native" }
                }
            ]

        print(f"正在调用多文档RAG流式LLM API: {llm_config.get('model')}")
        
        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=60,
            stream=True
        )

        if response.status_code == 200:
            # 返回流式响应的生成器
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        if data_str.strip() == '[DONE]':
                            break
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data and len(data['choices']) > 0:
                                delta = data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    yield delta['content']
                        except json.JSONDecodeError:
                            continue
        else:
            print(f"多文档RAG流式API调用失败: {response.status_code}, {response.text}")
            yield f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"多文档RAG流式LLM API调用错误: {e}")
        yield "抱歉，AI助手遇到了技术问题，请稍后再试。"

@app.route('/chat_multi_doc_stream', methods=['POST'])
def chat_multi_doc_stream():
    """跨文档分析流式聊天API"""
    data = request.get_json()
    message = data.get('message', '').strip()
    main_paper_id = data.get('main_paper_id')
    reference_paper_ids = data.get('reference_paper_ids', [])
    chat_history = data.get('chat_history', [])
    
    if not message:
        return jsonify({'error': '消息不能为空'}), 400
    
    if not main_paper_id:
        return jsonify({'error': '缺少主论文ID'}), 400
    
    try:
        # 获取论文信息
        papers = load_papers()
        main_paper = next((p for p in papers if p['id'] == main_paper_id), None)
        
        if not main_paper:
            return jsonify({'error': '主论文不存在'}), 404
        
        # 获取参考论文信息
        reference_papers = []
        for ref_id in reference_paper_ids:
            ref_paper = next((p for p in papers if p['id'] == ref_id), None)
            if ref_paper:
                reference_papers.append(ref_paper)
        
        # 构建对话消息列表
        messages = []
        
        # 添加历史对话
        for history_item in chat_history[-10:]:
            if history_item.get('role') and history_item.get('content'):
                messages.append({
                    'role': history_item['role'],
                    'content': history_item['content']
                })
        
        # 添加当前用户消息
        messages.append({
            'role': 'user',
            'content': message
        })
        
        def generate():
            try:
                for chunk in call_llm_api_stream_with_multi_doc_rag(
                    messages, 
                    main_paper_id, 
                    reference_paper_ids,
                    main_paper, 
                    reference_papers
                ):
                    # 发送每个数据块
                    yield f"data: {json.dumps({'content': chunk, 'type': 'content'})}\n\n"
                
                # 发送完成信号
                yield f"data: {json.dumps({'type': 'done'})}\n\n"
                
            except Exception as e:
                print(f"跨文档流式聊天错误: {e}")
                yield f"data: {json.dumps({'content': '抱歉，出现了错误，请稍后重试。', 'type': 'error'})}\n\n"
                yield f"data: {json.dumps({'type': 'done'})}\n\n"
        
        return Response(
            generate(),
            mimetype='text/event-stream',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Content-Type'
            }
        )
        
    except Exception as e:
        print(f"跨文档流式聊天API错误: {e}")
        return jsonify({
            'error': '服务器内部错误，请稍后重试',
            'success': False
        }), 500

@app.route('/api/switch_current_paper', methods=['POST'])
def api_switch_current_paper():
    """切换当前论文API"""
    try:
        data = request.get_json()
        new_current_paper_id = data.get('paper_id')
        
        if not new_current_paper_id:
            return jsonify({'error': '缺少论文ID'}), 400
        
        # 验证论文是否存在
        target_paper = db.session.get(Paper, new_current_paper_id)
        if not target_paper:
            return jsonify({'error': '论文不存在'}), 404
        
        target_paper_dict = target_paper.to_dict()
        
        # 确保论文有预阅读
        if not rag_service.get_or_create_rag_context(new_current_paper_id, target_paper_dict):
            return jsonify({'error': '论文的预阅读生成失败'}), 500
        
        return jsonify({
            'success': True,
            'current_paper_id': new_current_paper_id,
            'paper_info': {
                'id': target_paper_dict['id'],
                'title': target_paper_dict['title'],
                'authors': target_paper_dict['authors'],
                'source': target_paper_dict['source'],
                'has_pdf': target_paper_dict.get('has_pdf', True)
            },
            'message': f'已切换到论文：{target_paper_dict["title"]}'
        })
        
    except Exception as e:
        print(f"切换当前论文错误: {e}")
        return jsonify({'error': '切换失败，请稍后重试'}), 500

@app.route('/api/clean_duplicates', methods=['POST'])
def api_clean_duplicates():
    """清理现有论文库中的重复论文"""
    try:
        papers = load_papers()
        original_count = len(papers)
        
        # 使用文件哈希去重
        cleaned_papers = remove_duplicate_papers(papers)
        cleaned_count = len(cleaned_papers)
        
        if cleaned_count < original_count:
            # 保存清理后的论文列表
            save_papers(cleaned_papers)
            
            return jsonify({
                'success': True,
                'message': f'成功清理重复论文，从 {original_count} 篇减少到 {cleaned_count} 篇',
                'removed_count': original_count - cleaned_count,
                'remaining_count': cleaned_count
            })
        else:
            return jsonify({
                'success': True,
                'message': '未发现重复论文',
                'removed_count': 0,
                'remaining_count': original_count
            })
        
    except Exception as e:
        print(f"清理重复论文错误: {e}")
        return jsonify({
            'error': '清理重复论文时发生错误',
            'success': False
        }), 500


# ========== 多Agent论文阅读功能 ==========

@app.route('/multi_agent_analysis/<int:paper_id>', methods=['POST'])
def start_multi_agent_analysis(paper_id):
    """启动多Agent论文分析"""
    try:
        # 获取论文信息
        paper = db.session.get(Paper, paper_id)
        if not paper:
            return jsonify({'error': '论文不存在'}), 404
        
        # 检查PDF文件
        if not paper.file_path or not os.path.exists(paper.file_path):
            return jsonify({'error': '论文PDF文件不存在'}), 404
        
        # 提取PDF内容
        pdf_content = ""
        try:
            with open(paper.file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    pdf_content += page.extract_text() + "\n"
        except Exception as e:
            print(f"PDF提取错误: {e}")
            return jsonify({'error': 'PDF内容提取失败'}), 500
        
        # 创建阅读会话
        session_id = multi_agent_reader.create_reading_session(
            paper_id=paper_id,
            paper_content=pdf_content,
            paper_metadata=paper.to_dict()
        )
        
        # 获取用户查询（可选）
        data = request.get_json() or {}
        user_query = data.get('query', '')
        
        # 运行多Agent分析
        analysis_result = multi_agent_reader.run_multi_agent_analysis(
            session_id=session_id,
            paper_content=pdf_content,
            paper_metadata=paper.to_dict(),
            user_query=user_query
        )
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'analysis_result': analysis_result,
            'message': '多Agent分析完成'
        })
        
    except Exception as e:
        print(f"多Agent分析错误: {e}")
        return jsonify({
            'error': '多Agent分析失败，请稍后重试',
            'success': False
        }), 500


@app.route('/multi_agent_continue/<session_id>', methods=['POST'])
def continue_multi_agent_analysis(session_id):
    """继续多Agent分析"""
    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({'error': '缺少查询内容'}), 400
        
        new_query = data['query']
        
        # 从会话中获取论文信息
        session_history = multi_agent_reader.get_session_history(session_id)
        if not session_history:
            return jsonify({'error': '会话不存在'}), 404
        
        paper_id = session_history['paper_id']
        paper = db.session.get(Paper, paper_id)
        if not paper:
            return jsonify({'error': '论文不存在'}), 404
        
        # 提取PDF内容
        pdf_content = ""
        try:
            with open(paper.file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    pdf_content += page.extract_text() + "\n"
        except Exception as e:
            print(f"PDF提取错误: {e}")
            return jsonify({'error': 'PDF内容提取失败'}), 500
        
        # 继续分析
        analysis_result = multi_agent_reader.continue_analysis(
            session_id=session_id,
            new_query=new_query,
            paper_content=pdf_content,
            paper_metadata=paper.to_dict()
        )
        
        return jsonify({
            'success': True,
            'analysis_result': analysis_result,
            'message': '深入分析完成'
        })
        
    except Exception as e:
        print(f"继续分析错误: {e}")
        return jsonify({
            'error': '分析失败，请稍后重试',
            'success': False
        }), 500


@app.route('/multi_agent_history/<session_id>', methods=['GET'])
def get_multi_agent_history(session_id):
    """获取多Agent分析历史"""
    try:
        history = multi_agent_reader.get_session_history(session_id)
        if not history:
            return jsonify({'error': '会话不存在'}), 404
        
        return jsonify({
            'success': True,
            'history': history
        })
        
    except Exception as e:
        print(f"获取历史错误: {e}")
        return jsonify({
            'error': '获取历史失败',
            'success': False
        }), 500


@app.route('/multi_agent_report/<session_id>', methods=['GET'])
def get_multi_agent_report(session_id):
    """获取多Agent分析综合报告"""
    try:
        report = multi_agent_reader.generate_final_report(session_id)
        
        return jsonify({
            'success': True,
            'report': report
        })
        
    except Exception as e:
        print(f"生成报告错误: {e}")
        return jsonify({
            'error': '生成报告失败',
            'success': False
        }), 500


@app.route('/multi_agent_mindmap/<session_id>', methods=['GET'])
def get_multi_agent_mindmap(session_id):
    """获取多Agent分析思维导图"""
    try:
        mindmap_code = multi_agent_reader.generate_mindmap(session_id)
        
        print(f"生成的思维导图代码长度: {len(mindmap_code) if mindmap_code else 0}")
        print(f"思维导图代码预览: {mindmap_code[:200] if mindmap_code else 'None'}...")
        
        if not mindmap_code:
            return jsonify({
                'error': '暂无分析数据生成思维导图',
                'success': False
            }), 404
        
        return jsonify({
            'success': True,
            'mindmap_code': mindmap_code
        })
        
    except Exception as e:
        print(f"生成思维导图错误: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'error': '生成思维导图失败',
            'success': False
        }), 500


@app.route('/multi_agent_chat/<int:paper_id>')
def multi_agent_chat_page(paper_id):
    """多Agent聊天页面"""
    try:
        paper = db.session.get(Paper, paper_id)
        if not paper:
            return redirect(url_for('index'))
        
        return render_template('multi_agent_chat.html', paper=paper.to_dict())
        
    except Exception as e:
        print(f"多Agent聊天页面错误: {e}")
        return redirect(url_for('index'))


if __name__ == '__main__':
    # 在后台线程中启动ArXiv ID查找任务
    # arxiv_thread = threading.Thread(target=find_missing_arxiv_ids_task)
    # arxiv_thread.daemon = True
    # arxiv_thread.start()
    
    app.run(debug=True, host='0.0.0.0', port=5001) 