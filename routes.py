from flask import (
    Blueprint, render_template, request, jsonify, redirect, url_for,
    send_file, Response, stream_with_context, current_app
)
import os
import json
import requests
import PyPDF2
import hashlib
from difflib import SequenceMatcher
import re
import base64
from datetime import datetime
import xml.etree.ElementTree as ET

from models import db, Paper, PaperHistory, DialogueMessage, Citation, TrackedPaperResult, PaperTrackJob
import fitz  # PyMuPDF
from celery_worker import celery # Import the celery object

main = Blueprint('main', __name__)


def call_llm_api(messages, paper_context=None):
    """调用LLM API进行对话"""
    try:
        llm_config = current_app.config['CONFIG'].get('llm', {})
        
        system_prompt = "You are a professional academic paper reading assistant..."
        # (rest of the prompt)

        if paper_context:
            system_prompt += f"Title: {paper_context.get('title', 'Unknown')}..."
            # (rest of context building)

        api_messages = [{"role": "system", "content": system_prompt}]
        api_messages.extend(messages)

        headers = {
            'Authorization': f"Bearer {llm_config.get('api_key', '')}",
            'Content-Type': 'application/json',
            'HTTP-Referer': 'http://localhost:5001',
            'X-Title': 'DocuMancer'
        }

        payload = {
            'model': llm_config.get('model', 'google/gemini-2.5-flash-lite-preview-06-17'),
            'messages': api_messages,
            'max_tokens': llm_config.get('max_tokens', 2048),
            'temperature': llm_config.get('temperature', 0.7),
            'stream': False
        }
        
        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers, json=payload, timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                return result['choices'][0]['message']['content']
        # (Error handling)
        return "Sorry, the AI assistant is currently unavailable."

    except Exception as e:
        print(f"LLM API call error: {e}")
        return "Sorry, the AI assistant encountered a technical issue."


def load_papers():
    """加载已保存的论文数据"""
    papers = Paper.query.all()
    return [paper.to_dict() for paper in papers]


def extract_pdf_info(file_path):
    """从PDF文件提取论文信息"""
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            metadata = pdf_reader.metadata
            title = metadata.get('/Title', 'Unknown Title')
            author = metadata.get('/Author', 'Unknown Author')
            return {
                'title': title,
                'authors': author,
                'abstract': 'Abstract not extracted in this version.',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'source': 'PDF Upload',
                'has_pdf': True
            }
    except Exception as e:
        print(f"PDF parsing error: {e}")
        return None

# ... (Include other necessary helper functions from app.py, like is_duplicate_paper, etc.)
# Make sure to replace global variables like `rag_service` with `current_app.rag_service`

@main.route('/')
def index():
    """论文库主页"""
    papers = load_papers()
    return render_template('index.html', papers=papers)

@main.route('/upload_pdf', methods=['POST'])
def upload_pdf():
    # ... (code from app.py, using current_app.config['UPLOAD_FOLDER'])
    pass


@main.route('/deep_research/upload', methods=['POST'])
def deep_research_upload():
    if 'pdf_file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
    file = request.files['pdf_file']
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
    
    if file and file.filename.lower().endswith('.pdf'):
        # For simplicity, user_id is not handled in this MVP
        
        # Save file temporarily to extract title
        temp_path = os.path.join(current_app.config['UPLOAD_FOLDER'], f"temp_{file.filename}")
        file.seek(0)
        file.save(temp_path)

        try:
            with fitz.open(temp_path) as doc:
                title = doc.metadata.get('title', file.filename)
                if not title.strip():
                    title = file.filename
        except Exception as e:
            title = file.filename
            print(f"Could not extract title from PDF: {e}")
        
        # Create a job record in the database
        new_job = PaperTrackJob(
            original_paper_title=title,
            status='PENDING'
        )
        db.session.add(new_job)
        db.session.commit()

        # Save the PDF with a name based on the job ID
        final_filename = f"paper_track_{new_job.id}.pdf"
        final_path = os.path.join(current_app.config['UPLOAD_FOLDER'], final_filename)
        os.rename(temp_path, final_path)

        # Trigger the asynchronous task using send_task
        celery.send_task('celery_worker.process_paper_track', args=[new_job.id, final_path])

        # Redirect to the results page, which will poll for status
        return redirect(url_for('main.deep_research_result_page', job_id=new_job.id))

    return jsonify({'error': 'Invalid file type, please upload a PDF'}), 400


@main.route('/deep_research/status/<int:job_id>')
def deep_research_status(job_id):
    job = PaperTrackJob.query.get(job_id)
    if not job:
        return jsonify({'status': 'NOT_FOUND'}), 404
    return jsonify({'status': job.status})


@main.route('/deep_research/result/<int:job_id>')
def deep_research_result(job_id):
    job = PaperTrackJob.query.get(job_id)
    if not job or job.status != 'COMPLETED':
        return jsonify({'error': 'Job not found or not completed'}), 404

    result = TrackedPaperResult.query.filter_by(job_id=job.id).first()
    if not result:
        return jsonify({'error': 'Result not found for this job'}), 404

    return jsonify({
        'job_id': job.id,
        'original_paper_title': job.original_paper_title,
        'status': job.status,
        'main_paper_info': result.main_paper_info,
        'references_list': result.references_list,
        'cited_by_list': result.cited_by_list,
    })


@main.route('/deep_research/result_page/<int:job_id>')
def deep_research_result_page(job_id):
    return render_template('deep_research_result.html', job_id=job_id)


@main.route('/deep_research')
def deep_research_upload_page():
    return render_template('deep_research_upload.html')

# (Add all other routes from app.py here, adapted for the blueprint) 