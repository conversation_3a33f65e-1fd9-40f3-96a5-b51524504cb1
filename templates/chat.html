<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ paper.title }} - DocuMancer</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- 左侧功能栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="app-logo">
                    <h1 class="app-name">DocuMancer</h1>
                    <p class="app-name-chinese">论文精灵</p>
                </div>
            </div>
            <div class="sidebar-content">
                <!-- 返回论文库按钮 -->
                <button class="back-button" onclick="goBack()">
                    <span class="back-icon">←</span>
                    返回论文库
                </button>
                
                <!-- 添加论文到对话中按钮 -->
                <div class="multi-doc-section">
                    <button class="multi-doc-button" onclick="openMultiDocModal()">
                        <span class="multi-doc-icon">➕</span>
                        添加论文到对话中
                    </button>
                    <p class="multi-doc-desc">选择论文添加到当前对话</p>
                </div>
                
                <!-- 切换到多Agent论文分析按钮 -->
                <div class="multi-agent-section">
                    <button class="multi-agent-button" onclick="goToMultiAgent({{ paper.id }})">
                        <span class="multi-agent-icon">🤖</span>
                        多Agent论文分析
                    </button>
                    <p class="multi-agent-desc">使用多个AI Agent协作分析论文</p>
                </div>
                
                <!-- 论文信息（集成预阅读功能） -->
                <div class="paper-info" id="currentPaperInfo">
                    <h3 class="info-title">当前论文</h3>
                    <div class="paper-details">
                        <p class="paper-title-sidebar">{{ paper.title }}</p>
                        <p class="paper-authors-sidebar">{{ paper.authors }}</p>
                        <p class="paper-source-sidebar">{{ paper.source }}</p>
                    </div>
                    
                    <!-- 集成的预阅读功能 -->
                    {% if paper.has_pdf %}
                    <div class="integrated-preread">
                        <div class="preread-actions">
                            <button class="preread-btn" onclick="goToPreRead({{ paper.id }})">
                                <span class="btn-icon">📚</span>
                                查看预阅读
                            </button>
                        </div>
                        <p class="preread-hint">AI深度分析论文内容</p>
                    </div>
                    {% endif %}
                </div>
                
                <!-- 参考论文卡片 - 每篇论文都是独立的卡片 -->
                <div id="referencePapersList" class="reference-papers-list">
                    <!-- 动态生成的参考论文卡片 -->
                </div>
            </div>
        </div>

        <!-- 主内容区域 - 左右分栏 -->
        <div class="main-content">
            <!-- 左边：聊天对话框 -->
            <div class="chat-panel">
                <div class="chat-container">
                    <div class="chat-header">
                        <h2 class="chat-title">与论文对话</h2>
                        <p class="chat-subtitle">{{ paper.title }}</p>
                        <!-- 跨文档分析状态指示 -->
                        <div id="multiDocStatus" class="multi-doc-status" style="display: none;">
                            <span class="status-icon">🔗</span>
                            <span class="status-text">多论文对话模式</span>
                            <button class="exit-multi-doc" onclick="exitMultiDocMode()">退出</button>
                        </div>
                    </div>
                    
                    <!-- 聊天消息区域 -->
                    <div id="chatMessages" class="chat-messages">
                        <div class="welcome-message">
                            <div class="message assistant">
                                <div class="message-avatar">🤖</div>
                                <div class="message-content">
                                    您好！我是您的论文阅读助手。我已经了解了这篇论文的内容，您可以向我询问关于这篇论文的任何问题，比如：
                                    
                                    <div class="suggested-questions">
                                        <button class="question-suggestion" onclick="sendSuggestedQuestion('这篇论文的主要贡献是什么？')">
                                            这篇论文的主要贡献是什么？
                                        </button>
                                        <button class="question-suggestion" onclick="sendSuggestedQuestion('请解释一下论文中的核心方法')">
                                            请解释一下论文中的核心方法
                                        </button>
                                        <button class="question-suggestion" onclick="sendSuggestedQuestion('这篇论文有哪些局限性？')">
                                            这篇论文有哪些局限性？
                                        </button>
                                        <button class="question-suggestion" onclick="sendSuggestedQuestion('相关工作有哪些？')">
                                            相关工作有哪些？
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 输入区域 -->
                    <div class="chat-input-area">
                        <div class="input-container">
                            <input 
                                type="text" 
                                id="messageInput" 
                                placeholder="向论文提问..." 
                                class="message-input"
                            >
                            <button id="sendButton" class="send-button">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="m22 2-7 20-4-9-9-4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右边：PDF阅读器 -->
            <div class="pdf-panel">
                <div class="pdf-header">
                    <h3 class="pdf-title">论文原文</h3>
                    <div class="pdf-controls">
                        <button class="pdf-control-btn" id="prevPageBtn" title="上一页">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M15 18l-6-6 6-6"/>
                            </svg>
                        </button>
                        <span class="page-indicator">
                            Page <span id="page_num"></span> / <span id="page_count"></span>
                        </span>
                        <button class="pdf-control-btn" id="nextPageBtn" title="下一页">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 18l6-6-6-6"/>
                            </svg>
                        </button>
                        <button class="pdf-control-btn" id="zoomOutBtn" title="缩小">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                                <line x1="8" y1="11" x2="14" y2="11"></line>
                            </svg>
                        </button>
                        <span class="zoom-level" id="zoomLevel">100%</span>
                        <button class="pdf-control-btn" id="zoomInBtn" title="放大">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                                <line x1="11" y1="8" x2="11" y2="14"></line>
                                <line x1="8" y1="11" x2="14" y2="11"></line>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="pdf-viewer-container">
                    <div id="pdf-viewer" style="width: 100%; height: 100%; overflow: auto;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 跨文档选择弹窗 -->
    <div id="multiDocModal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>选择要添加的论文</h3>
                <button class="modal-close" onclick="closeMultiDocModal()">×</button>
            </div>
            <div class="modal-body">
                <p class="modal-desc">选择一篇或多篇论文添加到当前对话中</p>
                <div class="paper-list" id="paperList">
                    <!-- 论文列表将通过JavaScript动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-btn secondary" onclick="closeMultiDocModal()">取消</button>
                <button class="modal-btn primary" onclick="startMultiDocAnalysis()">添加论文</button>
            </div>
        </div>
    </div>

    <script>
        const paperId = {{ paper.id }};
        const paperTitle = {{ paper.title|tojson }};
        const pdfUrl = "{{ url_for('view_pdf', paper_id=paper.id) }}";
    </script>
    <script src="{{ url_for('static', filename='chat.js') }}"></script>
    <script>
        // Click-to-Ask functionality
        let contextMenu = null;

        function createContextMenu() {
            if (contextMenu) return;
            contextMenu = document.createElement('div');
            contextMenu.className = 'context-menu';
            contextMenu.innerHTML = `
                <button data-action="explain">解释</button>
                <button data-action="summarize">总结</button>
                <button data-action="translate">翻译</button>
                <button data-action="related">相关概念</button>
            `;
            document.body.appendChild(contextMenu);

            contextMenu.addEventListener('click', (e) => {
                const button = e.target.closest('button');
                if (button) {
                    const action = button.dataset.action;
                    const selection = window.getSelection();
                    let selectedText = selection.toString().trim();
                    if (!selectedText) {
                        // Fallback: try to get text from selected spans
                        if (selection.rangeCount > 0) {
                            const range = selection.getRangeAt(0);
                            selectedText = range.cloneContents().textContent.trim();
                        }
                    }
                    console.log('Selected text:', selectedText); // For debugging
                    let message = '';
                    switch (action) {
                        case 'explain':
                            message = `请解释一下：‘${selectedText}’`;
                            break;
                        case 'summarize':
                            message = `请总结一下：‘${selectedText}’`;
                            break;
                        case 'translate':
                            message = `请将以下内容翻译成中文：‘${selectedText}’`;
                            break;
                        case 'related':
                            message = `请提供与‘${selectedText}’相关的概念或延伸资料`;
                            break;
                    }
                    if (message && selectedText) {
                        sendMessage(message);
                    } else {
                        console.warn('No valid selected text');
                    }
                    hideContextMenu();
                    selection.removeAllRanges();
                }
            });
        }

        function showContextMenu(x, y) {
            createContextMenu();
            contextMenu.style.left = `${x}px`;
            contextMenu.style.top = `${y}px`;
            contextMenu.style.display = 'block';
        }

        function hideContextMenu() {
            if (contextMenu) {
                contextMenu.style.display = 'none';
            }
        }

        document.addEventListener('mousedown', (e) => {
            if (contextMenu && contextMenu.style.display === 'block' && !contextMenu.contains(e.target)) {
                hideContextMenu();
            }
        });

        document.getElementById('pdf-viewer').addEventListener('mouseup', (e) => {
            setTimeout(() => {
                const selection = window.getSelection();
                if (selection.rangeCount > 0) {
                    let selectedText = selection.toString().trim();
                    if (!selectedText) {
                        const range = selection.getRangeAt(0);
                        selectedText = range.cloneContents().textContent.trim();
                    }
                    if (selectedText) {
                        const range = selection.getRangeAt(0);
                        const rect = range.getBoundingClientRect();
                        const x = rect.right + window.pageXOffset;
                        const y = rect.bottom + window.pageYOffset;
                        showContextMenu(x, y);
                    } else {
                        hideContextMenu();
                    }
                } else {
                    hideContextMenu();
                }
            }, 100); // Increased delay for selection to register
        });
    </script>
</body>
</html> 