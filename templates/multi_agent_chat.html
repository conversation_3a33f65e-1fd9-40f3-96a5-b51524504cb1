<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多Agent论文分析 - {{ paper.title }} - DocuMancer</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.min.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- 左侧功能栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="app-logo">
                    <h1 class="app-name">DocuMancer</h1>
                    <p class="app-name-chinese">论文精灵</p>
                </div>
            </div>
            <div class="sidebar-content">
                <!-- 返回论文库按钮 -->
                <button class="back-button" onclick="goBack()">
                    <span class="back-icon">←</span>
                    返回论文库
                </button>

                <!-- 选择论文功能 -->
                <div class="paper-selection-section">
                    <button class="paper-select-button" onclick="openPaperSelectionModal()">
                        <span class="select-icon">📚</span>
                        选择论文
                    </button>
                    <p class="select-desc">选择要分析的论文</p>
                </div>

                <!-- 切换到普通聊天按钮 -->
                <div class="multi-agent-section">
                    <button class="back-button chat-switch-btn" onclick="goToChat({{ paper.id }})">
                        <span class="back-icon">💬</span>
                        切换到普通聊天
                    </button>
                    <p class="multi-agent-desc">切换到普通聊天</p>
                </div>
                
                <!-- 论文信息 -->
                <div class="paper-info">
                    <h3 class="info-title">当前论文</h3>
                    <div class="paper-details">
                        <p class="paper-title-sidebar" id="currentPaperTitle">{{ paper.title }}</p>
                        <p class="paper-authors-sidebar" id="currentPaperAuthors">{{ paper.authors }}</p>
                        <p class="paper-source-sidebar" id="currentPaperSource">{{ paper.source }}</p>
                    </div>
                </div>
                
                <!-- 多Agent控制面板 -->
                <div class="multi-agent-controls">
                    <h3 class="control-title">分析配置</h3>
                    
                    <div class="control-group">
                        <label class="control-label">分析类型</label>
                        <select id="analysisType" class="control-select">
                            <option value="comprehensive">全面分析</option>
                            <option value="methodology">方法分析</option>
                            <option value="experiments">实验分析</option>
                            <option value="comparison">对比分析</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">Agent配置</label>
                        <div class="agent-toggles">
                            <label class="agent-toggle">
                                <input type="checkbox" id="analystAgent" checked>
                                <span class="toggle-label">📊 分析师</span>
                            </label>
                            <label class="agent-toggle">
                                <input type="checkbox" id="summarizerAgent" checked>
                                <span class="toggle-label">📝 总结者</span>
                            </label>
                            <label class="agent-toggle">
                                <input type="checkbox" id="criticAgent" checked>
                                <span class="toggle-label">🔍 批评者</span>
                            </label>
                            <label class="agent-toggle">
                                <input type="checkbox" id="coordinatorAgent" checked>
                                <span class="toggle-label">🎯 协调者</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="control-group">
                        <label class="control-label">输出格式</label>
                        <div class="output-options">
                            <label class="output-option">
                                <input type="radio" name="outputFormat" value="text" checked>
                                <span class="option-label">文本报告</span>
                            </label>
                            <label class="output-option">
                                <input type="radio" name="outputFormat" value="mindmap">
                                <span class="option-label">思维导图</span>
                            </label>
                            <label class="output-option">
                                <input type="radio" name="outputFormat" value="structured">
                                <span class="option-label">结构化</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 - 左右分栏 -->
        <div class="main-content">
            <!-- 左边：聊天对话框 -->
            <div class="chat-panel">
                <div class="chat-container">
                    <div class="chat-header">
                        <h2 class="chat-title">🤖 多Agent论文分析</h2>
                        <p class="chat-subtitle" id="chatSubtitle">{{ paper.title }}</p>
                        <div class="multi-agent-status">
                            <span class="status-icon">🤖</span>
                            <span class="status-text">多Agent协作分析模式</span>
                        </div>
                    </div>
                    
                    <!-- 聊天消息区域 -->
                    <div id="chatMessages" class="chat-messages">
                        <div class="welcome-message">
                            <div class="message assistant">
                                <div class="message-avatar">🤖</div>
                                <div class="message-content">
                                    <h3>欢迎使用多Agent论文分析系统！</h3>
                                    <p>我将通过多个专业Agent协作分析这篇论文，为您提供深度见解。</p>
                                    
                                    <div class="agent-introduction">
                                        <div class="agent-card">
                                            <div class="agent-icon">📊</div>
                                            <div class="agent-info">
                                                <h4>分析师</h4>
                                                <p>深入分析论文的方法、创新点和贡献</p>
                                            </div>
                                        </div>
                                        <div class="agent-card">
                                            <div class="agent-icon">📝</div>
                                            <div class="agent-info">
                                                <h4>总结者</h4>
                                                <p>提炼核心观点和关键发现</p>
                                            </div>
                                        </div>
                                        <div class="agent-card">
                                            <div class="agent-icon">🔍</div>
                                            <div class="agent-info">
                                                <h4>批评者</h4>
                                                <p>识别局限性和潜在问题</p>
                                            </div>
                                        </div>
                                        <div class="agent-card">
                                            <div class="agent-icon">🎯</div>
                                            <div class="agent-info">
                                                <h4>协调者</h4>
                                                <p>整合各Agent观点，提供综合建议</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="suggested-questions">
                                        <h4>您可以这样开始：</h4>
                                        <button class="question-suggestion" onclick="startAnalysis('comprehensive')">
                                            🚀 开始全面分析
                                        </button>
                                        <button class="question-suggestion" onclick="startAnalysis('methodology')">
                                            🔬 分析方法论
                                        </button>
                                        <button class="question-suggestion" onclick="startAnalysis('experiments')">
                                            🧪 分析实验设计
                                        </button>
                                        <button class="question-suggestion" onclick="startAnalysis('comparison')">
                                            ⚖️ 与相关工作对比
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 输入区域 -->
                    <div class="chat-input-area">
                        <div class="input-container">
                            <textarea 
                                id="messageInput" 
                                placeholder="输入您的分析需求或问题..." 
                                class="message-input"
                                rows="1"
                            ></textarea>
                            <button id="sendButton" class="send-button">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="m22 2-7 20-4-9-9-4z"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右边：PDF阅读器 -->
            <div class="pdf-panel">
                <div class="pdf-header">
                    <h3 class="pdf-title">论文原文</h3>
                    <div class="pdf-controls">
                        <button class="pdf-control-btn" id="prevPageBtn" title="上一页">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M15 18l-6-6 6-6"/>
                            </svg>
                        </button>
                        <span class="page-indicator">
                            Page <span id="page_num"></span> / <span id="page_count"></span>
                        </span>
                        <button class="pdf-control-btn" id="nextPageBtn" title="下一页">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M9 18l6-6-6-6"/>
                            </svg>
                        </button>
                        <button class="pdf-control-btn" id="zoomOutBtn" title="缩小">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                                <line x1="8" y1="11" x2="14" y2="11"></line>
                            </svg>
                        </button>
                        <span class="zoom-level" id="zoomLevel">100%</span>
                        <button class="pdf-control-btn" id="zoomInBtn" title="放大">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"></circle>
                                <path d="m21 21-4.35-4.35"></path>
                                <line x1="11" y1="8" x2="11" y2="14"></line>
                                <line x1="8" y1="11" x2="14" y2="11"></line>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="pdf-viewer-container">
                    <div id="pdf-viewer" style="width: 100%; height: 100%; overflow: auto;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 论文选择模态框 -->
    <div id="paperSelectionModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>选择论文</h3>
                <button class="close-button" onclick="closePaperSelectionModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="search-container">
                    <input type="text" id="paperSearchInput" placeholder="搜索论文..." class="search-input">
                </div>
                <div id="paperList" class="paper-list">
                    <!-- 论文列表将在这里动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="cancel-button" onclick="closePaperSelectionModal()">取消</button>
                <button class="confirm-button" onclick="confirmPaperSelection()">确认选择</button>
            </div>
        </div>
    </div>

    <script>
        let currentPaperId = {{ paper.id }};
        let currentPaperTitle = {{ paper.title|tojson }};
        let currentPdfUrl = "{{ url_for('view_pdf', paper_id=paper.id) }}";
        const pdfUrl = "{{ url_for('view_pdf', paper_id=paper.id) }}";
        let currentSessionId = null;
        let selectedPaperId = null;
        let papersData = [];
        
        // 初始化Mermaid
        mermaid.initialize({ startOnLoad: true });
        
        // 返回论文库
        function goBack() {
            window.location.href = '/';
        }
        
        // 切换到普通聊天
        function goToChat(paperId) {
            window.location.href = '/chat/' + paperId;
        }
        
        // 打开论文选择模态框
        function openPaperSelectionModal() {
            document.getElementById('paperSelectionModal').style.display = 'flex';
            loadPaperList();
        }
        
        // 关闭论文选择模态框
        function closePaperSelectionModal() {
            document.getElementById('paperSelectionModal').style.display = 'none';
            selectedPaperId = null;
        }
        
        // 加载论文列表
        async function loadPaperList() {
            try {
                const response = await fetch('/api/papers');
                const data = await response.json();
                
                if (data.success) {
                    papersData = data.papers;
                    renderPaperList(data.papers);
                } else {
                    console.error('获取论文列表失败');
                }
            } catch (error) {
                console.error('加载论文列表错误:', error);
            }
        }
        
        // 渲染论文列表
        function renderPaperList(papers) {
            const paperList = document.getElementById('paperList');
            const searchTerm = document.getElementById('paperSearchInput').value.toLowerCase();
            
            const filteredPapers = papers.filter(paper => 
                paper.title.toLowerCase().includes(searchTerm) ||
                paper.authors.toLowerCase().includes(searchTerm)
            );
            
            paperList.innerHTML = filteredPapers.map(paper => `
                <div class="paper-item ${paper.id === currentPaperId ? 'current-paper' : ''}" 
                     onclick="selectPaper(${paper.id})">
                    <div class="paper-item-content">
                        <div class="paper-item-title">${paper.title}</div>
                        <div class="paper-item-authors">${paper.authors}</div>
                        <div class="paper-item-source">${paper.source}</div>
                    </div>
                    <div class="paper-item-checkbox">
                        <input type="radio" name="selectedPaper" value="${paper.id}" 
                               ${paper.id === selectedPaperId ? 'checked' : ''}>
                    </div>
                </div>
            `).join('');
        }
        
        // 选择论文
        function selectPaper(paperId) {
            selectedPaperId = paperId;
            // 更新单选按钮状态
            document.querySelectorAll('input[name="selectedPaper"]').forEach(radio => {
                radio.checked = radio.value == paperId;
            });
        }
        
        // 确认论文选择
        function confirmPaperSelection() {
            if (!selectedPaperId) {
                alert('请选择一篇论文');
                return;
            }
            
            const selectedPaper = papersData.find(p => p.id == selectedPaperId);
            if (selectedPaper) {
                // 更新当前论文信息
                currentPaperId = selectedPaper.id;
                currentPaperTitle = selectedPaper.title;
                currentPdfUrl = `/view_pdf/${selectedPaper.id}`;
                
                // 更新界面显示
                document.getElementById('currentPaperTitle').textContent = selectedPaper.title;
                document.getElementById('currentPaperAuthors').textContent = selectedPaper.authors;
                document.getElementById('currentPaperSource').textContent = selectedPaper.source;
                document.getElementById('chatSubtitle').textContent = selectedPaper.title;
                
                // 重新初始化PDF查看器
                setTimeout(function() {
                    console.log('Switching to new PDF:', currentPdfUrl);
                    if (window.updatePdfDisplay) {
                        window.updatePdfDisplay(selectedPaper.id);
                    }
                }, 200);
                
                // 清空聊天记录
                document.getElementById('chatMessages').innerHTML = `
                    <div class="welcome-message">
                        <div class="message assistant">
                            <div class="message-avatar">🤖</div>
                            <div class="message-content">
                                <h3>已切换到论文：${selectedPaper.title}</h3>
                                <p>现在可以开始分析这篇论文了。</p>
                                
                                <div class="suggested-questions">
                                    <h4>您可以这样开始：</h4>
                                    <button class="question-suggestion" onclick="startAnalysis('comprehensive')">
                                        🚀 开始全面分析
                                    </button>
                                    <button class="question-suggestion" onclick="startAnalysis('methodology')">
                                        🔬 分析方法论
                                    </button>
                                    <button class="question-suggestion" onclick="startAnalysis('experiments')">
                                        🧪 分析实验设计
                                    </button>
                                    <button class="question-suggestion" onclick="startAnalysis('comparison')">
                                        ⚖️ 与相关工作对比
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                // 重置会话ID
                currentSessionId = null;
                
                closePaperSelectionModal();
            }
        }
        
        // 搜索论文
        document.getElementById('paperSearchInput').addEventListener('input', function() {
            renderPaperList(papersData);
        });
        
        // 开始分析
        function startAnalysis(type) {
            const messages = {
                'comprehensive': '请对这篇论文进行全面分析，包括研究背景、方法、实验、结果和贡献。',
                'methodology': '请详细分析这篇论文的方法论，包括核心算法、技术路线和创新点。',
                'experiments': '请分析这篇论文的实验设计、数据集、评估指标和实验结果。',
                'comparison': '请将这篇论文与相关工作进行对比分析，突出其优势和特点。'
            };
            sendMessage(messages[type]);
        }
        
        // 获取启用的Agent
        function getEnabledAgents() {
            const agents = [];
            if (document.getElementById('analystAgent').checked) agents.push('analyst');
            if (document.getElementById('summarizerAgent').checked) agents.push('summarizer');
            if (document.getElementById('criticAgent').checked) agents.push('critic');
            if (document.getElementById('coordinatorAgent').checked) agents.push('coordinator');
            return agents;
        }
        
        // 增强的Markdown渲染函数
        function renderMarkdown(text) {
            if (typeof text !== 'string') return text;
            
            return text
                // 代码块 (先处理，避免被其他规则影响)
                .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>')
                .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
                
                // 粗体
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/__(.*?)__/g, '<strong>$1</strong>')
                
                // 斜体
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/_(.*?)_/g, '<em>$1</em>')
                
                // 行内代码
                .replace(/`([^`]+)`/g, '<code>$1</code>')
                
                // 标题 (支持 # ## ### 等)
                .replace(/^### (.*$)/gm, '<h3>$1</h3>')
                .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                
                // 无序列表
                .replace(/^\* (.*$)/gm, '<li>$1</li>')
                .replace(/^- (.*$)/gm, '<li>$1</li>')
                .replace(/^(\d+)\. (.*$)/gm, '<li class="numbered">$2</li>')
                
                // 链接
                .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
                
                // 换行
                .replace(/\n/g, '<br>');
        }

        // 后处理：包装列表项
        function postProcessMarkdown(html) {
            // 将连续的<li>包装在<ul>中
            html = html.replace(/(<li(?:\s+class="[^"]*")?>[^<]*<\/li>(?:<br>)*)+/g, function(match) {
                const items = match.replace(/<br>/g, '');
                if (items.includes('class="numbered"')) {
                    return '<ol>' + items.replace(/class="numbered"/g, '') + '</ol>';
                } else {
                    return '<ul>' + items + '</ul>';
                }
            });
            
            // 清理多余的<br>标签
            html = html.replace(/<br><\/ul>/g, '</ul>');
            html = html.replace(/<br><\/ol>/g, '</ol>');
            html = html.replace(/<\/h[1-6]><br>/g, function(match) {
                return match.replace('<br>', '');
            });
            
            return html;
        }
        
        // 添加消息（带动画）
        function addMessageWithAnimation(content, sender, isError = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            if (isError) messageDiv.classList.add('error-message');
            
            // 初始状态
            messageDiv.style.opacity = '0';
            messageDiv.style.transform = 'translateY(20px)';
            messageDiv.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            
            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.textContent = sender === 'user' ? '👤' : '🤖';
            
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            
            // 处理消息内容格式
            if (typeof content === 'string') {
                let formattedContent = renderMarkdown(content);
                formattedContent = postProcessMarkdown(formattedContent);
                messageContent.innerHTML = formattedContent;
            } else {
                messageContent.textContent = content;
            }
            
            messageDiv.appendChild(avatar);
            messageDiv.appendChild(messageContent);
            
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.appendChild(messageDiv);
            
            // 触发动画
            requestAnimationFrame(() => {
                messageDiv.style.opacity = '1';
                messageDiv.style.transform = 'translateY(0)';
            });
            
            // 滚动到底部
            smoothScrollToBottom();
            
            return messageDiv;
        }
        
        // 平滑滚动到底部
        function smoothScrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            const targetScrollTop = chatMessages.scrollHeight - chatMessages.clientHeight;
            
            if (chatMessages.scrollTop !== targetScrollTop) {
                const startScrollTop = chatMessages.scrollTop;
                const distance = targetScrollTop - startScrollTop;
                const duration = 300;
                const startTime = performance.now();
                
                function animate(currentTime) {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);
                    
                    // 使用缓动函数
                    const easeOutCubic = 1 - Math.pow(1 - progress, 3);
                    chatMessages.scrollTop = startScrollTop + distance * easeOutCubic;
                    
                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    }
                }
                
                requestAnimationFrame(animate);
            }
        }
        
        // 添加加载消息
        function addLoadingMessage() {
            const chatMessages = document.getElementById('chatMessages');
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'message assistant loading';
            loadingDiv.innerHTML = `
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    <div class="loading-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <p>多Agent正在分析中...</p>
                </div>
            `;
            chatMessages.appendChild(loadingDiv);
            smoothScrollToBottom();
            return loadingDiv;
        }
        
        // 移除加载消息
        function removeLoadingMessage(loadingMessage) {
            if (loadingMessage && loadingMessage.parentNode) {
                loadingMessage.parentNode.removeChild(loadingMessage);
            }
        }
        
        // 发送消息
        async function sendMessage(providedMessage = null) {
            const messageInput = document.getElementById('messageInput');
            const message = providedMessage || messageInput.value.trim();
            
            if (!message) return;
            
            // 清空输入框
            if (!providedMessage) {
                messageInput.value = '';
            }
            
            // 添加用户消息
            addMessageWithAnimation(message, 'user');
            
            // 添加加载消息
            const loadingMessage = addLoadingMessage();
            
            try {
                // 发送请求
                const response = await fetch('/multi_agent_analysis/' + currentPaperId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: message
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentSessionId = data.session_id;
                    removeLoadingMessage(loadingMessage);
                    
                    // 显示分析结果
                    displayAnalysisResult(data.analysis_result);
                } else {
                    throw new Error(data.error || '分析失败');
                }
                
            } catch (error) {
                console.error('分析错误:', error);
                removeLoadingMessage(loadingMessage);
                addMessageWithAnimation('抱歉，分析过程中出现了错误：' + error.message, 'assistant', true);
            }
        }
        
        // 显示分析结果
        function displayAnalysisResult(result) {
            if (typeof result === 'string') {
                addMessageWithAnimation(result, 'assistant');
            } else if (result && typeof result === 'object') {
                // 如果是对象，尝试提取内容
                const content = result.content || result.text || result.message || JSON.stringify(result);
                addMessageWithAnimation(content, 'assistant');
            } else {
                addMessageWithAnimation('分析完成，但结果格式异常', 'assistant');
            }
        }
        
        // 绑定回车键
        document.getElementById('messageInput').addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // 绑定发送按钮
        document.getElementById('sendButton').addEventListener('click', sendMessage);
        



    </script>
    <script src="{{ url_for('static', filename='multi_agent_pdf.js') }}"></script>
</body>
</html>
