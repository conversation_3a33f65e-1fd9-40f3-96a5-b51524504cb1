# PDF切换问题修复文档

## 问题描述

在多Agent对话中，右侧PDF阅读器无法正常显示PDF，切换论文时显示报错：

```
PDF加载失败: ReferenceError: Cannot access 'pdfDoc' before initialization
```

## 问题分析

通过代码分析发现，问题的根源在于：

1. **变量初始化时机问题**：`pdfDoc`变量在PDF文档加载完成之前就被设置为`null`
2. **异步操作竞争条件**：在PDF加载过程中，`renderPage`函数可能被调用，但此时`pdfDoc`还未初始化
3. **状态管理不当**：切换论文时没有正确重置和同步PDF相关的状态变量

## 修复方案

### 1. 增强`renderPage`函数的健壮性

在`renderPage`函数开始时添加`pdfDoc`初始化检查：

```javascript
function renderPage(num) {
    console.log('Rendering page:', num);
    
    // 检查pdfDoc是否已初始化
    if (!pdfDoc) {
        console.error('PDF document not initialized');
        const pdfViewer = document.getElementById('pdf-viewer');
        if (pdfViewer) {
            pdfViewer.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">PDF文档未初始化，请稍后重试</p>';
        }
        return;
    }
    
    // ... 其余渲染逻辑
}
```

### 2. 改进`initializePdfViewer`函数

在PDF初始化时正确重置所有状态变量：

```javascript
function initializePdfViewer(url) {
    // ... 初始化代码
    
    // 重置状态
    pdfDoc = null;
    pageNum = 1;
    pageRendering = false;
    pageNumPending = null;
    
    // 显示加载状态
    const pdfViewer = document.getElementById('pdf-viewer');
    if (pdfViewer) {
        pdfViewer.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">正在加载PDF...</p>';
    }
    
    // ... PDF加载逻辑
}
```

### 3. 增强`queueRenderPage`函数

在队列渲染前检查PDF文档状态：

```javascript
function queueRenderPage(num) {
    if (!pdfDoc) {
        console.warn('PDF document not ready, cannot render page');
        return;
    }
    
    if (pageRendering) {
        pageNumPending = num;
    } else {
        renderPage(num);
    }
}
```

### 4. 改进控制按钮事件处理

在所有PDF控制按钮的事件处理中添加状态检查：

```javascript
// 缩放按钮
document.getElementById('zoomInBtn').addEventListener('click', function() {
    if (!pdfDoc || scaleMultiplier >= 3.0) return;
    scaleMultiplier += 0.2;
    queueRenderPage(pageNum);
});

// 窗口大小改变事件
window.addEventListener('resize', function() {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(function() {
        if (pdfDoc && pageNum && !pageRendering) {
            queueRenderPage(pageNum);
        }
    }, 150);
});
```

### 5. 优化论文切换逻辑

在切换论文时增加适当的延迟和日志：

```javascript
// 重新初始化PDF查看器
setTimeout(function() {
    console.log('Switching to new PDF:', currentPdfUrl);
    initializePdfViewer(currentPdfUrl);
}, 200);
```

## 修复效果

修复后的改进包括：

1. **错误处理**：在PDF未初始化时显示友好的错误信息，而不是抛出JavaScript错误
2. **状态同步**：确保所有PDF相关状态变量在切换时正确重置
3. **用户体验**：显示加载状态，让用户知道系统正在处理
4. **稳定性**：防止在PDF未准备好时执行渲染操作

## 测试建议

1. **快速切换测试**：连续快速切换不同论文，验证不会出现错误
2. **网络延迟测试**：在网络较慢的环境下测试PDF加载
3. **并发操作测试**：在PDF加载过程中尝试其他操作（如缩放、翻页）
4. **错误恢复测试**：模拟PDF加载失败的情况，验证错误处理

## 相关文件

- `templates/multi_agent_chat.html` - 主要修复文件
- `test_pdf_switching.html` - 测试页面（用于验证修复效果）

## 注意事项

1. 确保PDF.js库正确加载
2. 检查PDF文件路径是否正确
3. 验证服务器端PDF路由是否正常工作
4. 监控浏览器控制台的错误信息 