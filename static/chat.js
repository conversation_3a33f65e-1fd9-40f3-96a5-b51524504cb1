document.addEventListener('DOMContentLoaded', function() {
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    const chatMessages = document.getElementById('chatMessages');
    
    // 对话历史存储
    let chatHistory = [];
    
    // 多文档模式相关变量
    let multiDocMode = false;
    let selectedPapers = [];
    let activePapers = [];
    let papersData = [];
    let currentPaperId = paperId; // 当前论文ID，默认为初始论文

    // 加载聊天记录
    async function loadChatHistory() {
        try {
            const response = await fetch(`/api/get_chat_history/${paperId}`);
            if (response.ok) {
                const data = await response.json();
                if (data.history && data.history.length > 0) {
                    // 清除默认的欢迎消息
                    const welcomeMessage = document.querySelector('.welcome-message');
                    if (welcomeMessage) {
                        welcomeMessage.remove();
                    }
                    
                    chatHistory = data.history.map(msg => ({
                        role: msg.role,
                        content: msg.content
                    }));
                    
                    data.history.forEach(msg => {
                        addMessage(msg.content, msg.role);
                    });
                } else {
                     // 如果没有历史记录，添加建议问题
                    addSuggestedQuestions();
                }
            }
        } catch (error) {
            console.error('Error loading chat history:', error);
            addSuggestedQuestions();
        }
    }

    // 增强的Markdown渲染函数
    function renderMarkdown(text) {
        if (typeof text !== 'string') return text;
        
        return text
            // 代码块 (先处理，避免被其他规则影响)
            .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>')
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            
            // 粗体
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/__(.*?)__/g, '<strong>$1</strong>')
            
            // 斜体
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/_(.*?)_/g, '<em>$1</em>')
            
            // 行内代码
            .replace(/`([^`]+)`/g, '<code>$1</code>')
            
            // 标题 (支持 # ## ### 等)
            .replace(/^### (.*$)/gm, '<h3>$1</h3>')
            .replace(/^## (.*$)/gm, '<h2>$1</h2>')
            .replace(/^# (.*$)/gm, '<h1>$1</h1>')
            
            // 无序列表
            .replace(/^\* (.*$)/gm, '<li>$1</li>')
            .replace(/^- (.*$)/gm, '<li>$1</li>')
            .replace(/^(\d+)\. (.*$)/gm, '<li class="numbered">$2</li>')
            
            // 链接
            .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
            
            // 换行
            .replace(/\n/g, '<br>');
    }

    // 后处理：包装列表项
    function postProcessMarkdown(html) {
        // 将连续的<li>包装在<ul>中
        html = html.replace(/(<li(?:\s+class="[^"]*")?>[^<]*<\/li>(?:<br>)*)+/g, function(match) {
            const items = match.replace(/<br>/g, '');
            if (items.includes('class="numbered"')) {
                return '<ol>' + items.replace(/class="numbered"/g, '') + '</ol>';
            } else {
                return '<ul>' + items + '</ul>';
            }
        });
        
        // 清理多余的<br>标签
        html = html.replace(/<br><\/ul>/g, '</ul>');
        html = html.replace(/<br><\/ol>/g, '</ol>');
        html = html.replace(/<\/h[1-6]><br>/g, function(match) {
            return match.replace('<br>', '');
        });
        
        return html;
    }

    // --- AI 溯源高亮功能 ---
    let currentHighlights = [];

    /**
     * 清除所有AI高亮
     */
    function clearHighlights() {
        const highlightLayers = document.querySelectorAll('.ai-highlight');
        highlightLayers.forEach(layer => layer.remove());
        currentHighlights = [];
    }

    /**
     * 渲染AI高亮
     * @param {Array} sources - 来源信息数组
     */
    function renderHighlights(sources) {
        clearHighlights();
        currentHighlights = sources; // 存储当前高亮信息以备缩放时使用

        const pdfViewer = document.getElementById('pdfViewer');
        if (!pdfViewer) return;

        sources.forEach(source => {
            const pageNum = source.page_num;
            const bbox = source.bbox;

            const pageView = pdfViewer.querySelector(`.page[data-page-number="${pageNum}"]`);
            if (!pageView) return;

            const canvas = pageView.querySelector('canvas');
            const viewport = pdfjsLib.getViewport({ scale: pdfViewer.currentScale });

            if (!canvas || !viewport) return;

            const scale = canvas.offsetWidth / viewport.width;

            const highlightDiv = document.createElement('div');
            highlightDiv.className = 'ai-highlight';
            
            highlightDiv.style.left = `${bbox[0] * scale}px`;
            highlightDiv.style.top = `${bbox[1] * scale}px`;
            highlightDiv.style.width = `${(bbox[2] - bbox[0]) * scale}px`;
            highlightDiv.style.height = `${(bbox[3] - bbox[1]) * scale}px`;

            pageView.appendChild(highlightDiv);
        });
    }

    // 监听缩放变化，重新渲染高亮
    window.addEventListener('resize', () => {
        if (currentHighlights.length > 0) {
            renderHighlights(currentHighlights);
        }
    });
    // PDF.js的缩放事件也需要监听，这里假设有一个全局的EventBus或类似机制
    // 如果没有，需要在缩放函数中直接调用 renderHighlights
    // 此处简化处理，监听窗口resize

    // 发送消息函数（支持单文档和多文档模式）
    function sendMessage(providedMessage = null) {
        const message = providedMessage || messageInput.value.trim();
        if (!message) return;

        // 清除旧的AI高亮
        clearHighlights();

        // 添加用户消息
        addMessageWithAnimation(message, 'user');
        chatHistory.push({ role: 'user', content: message });
        
        if (!providedMessage) {
            // 清空输入框
            messageInput.value = '';
        }

        // 创建助手消息容器
        const assistantMessageDiv = document.createElement('div');
        assistantMessageDiv.className = 'message assistant';
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = '🤖';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = '<div class="streaming-content"></div>';
        
        assistantMessageDiv.appendChild(avatar);
        assistantMessageDiv.appendChild(messageContent);
        chatMessages.appendChild(assistantMessageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        const streamingContent = messageContent.querySelector('.streaming-content');
        let fullResponse = '';

        // 根据模式选择API端点和请求体
        let apiEndpoint, requestBody;
        
        if (multiDocMode && activePapers && activePapers.length > 0) {
            // 跨文档分析模式 - 使用流式API
            apiEndpoint = '/chat_multi_doc_stream';
            
            // 构建参考论文列表（排除当前论文）
            const referencePaperIds = activePapers.filter(id => id !== currentPaperId);
            
            requestBody = {
                message: message,
                main_paper_id: currentPaperId, // 使用当前论文作为主论文
                reference_paper_ids: referencePaperIds,
                chat_history: chatHistory
            };
        } else {
            // 普通单文档模式 - 使用流式API
            apiEndpoint = '/chat_stream';
            requestBody = {
                message: message,
                paper_id: paperId,
                chat_history: chatHistory
            };
        }

        // 使用流式API调用
        fetch(apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = ''; // 用于处理不完整的JSON数据
            
            function readChunk() {
                return reader.read().then(({ done, value }) => {
                    if (done) {
                        // 流式响应完成
                        assistantMessageDiv.classList.add('completed');
                        chatHistory.push({ role: 'assistant', content: fullResponse });
                        
                        // 添加完成动画
                        setTimeout(() => {
                            assistantMessageDiv.style.transform = 'scale(1.02)';
                            setTimeout(() => {
                                assistantMessageDiv.style.transform = 'scale(1)';
                            }, 150);
                        }, 100);
                        return;
                    }
                    
                    // 解析流式数据
                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;
                    
                    // 处理可能包含多个数据块的情况
                    const lines = buffer.split('\n');
                    buffer = lines.pop() || ''; // 保留最后一个可能不完整的行
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                
                                if (data.type === 'sources') {
                                    // 渲染溯源信息
                                    renderHighlights(data.data);
                                    // 添加溯源提示动画
                                    showNotification('已找到相关文献引用', 'info');
                                } else if (data.type === 'content' && data.content) {
                                    fullResponse += data.content;
                                    
                                    // 处理Markdown并更新内容
                                    const processedText = postProcessMarkdown(renderMarkdown(fullResponse));
                                    updateStreamingContent(processedText, streamingContent);
                                    
                                    // 平滑滚动到底部
                                    smoothScrollToBottom();
                                } else if (data.type === 'error') {
                                    const errorText = postProcessMarkdown(renderMarkdown(data.content || '出现错误'));
                                    streamingContent.innerHTML = errorText;
                                    assistantMessageDiv.classList.add('error-message');
                                    
                                    // 错误提示动画
                                    assistantMessageDiv.style.animation = 'shake 0.5s ease-in-out';
                                    setTimeout(() => {
                                        assistantMessageDiv.style.animation = '';
                                    }, 500);
                                    return;
                                } else if (data.type === 'done') {
                                    assistantMessageDiv.classList.add('completed');
                                    chatHistory.push({ role: 'assistant', content: fullResponse });
                                    return;
                                }
                            } catch (e) {
                                // 忽略JSON解析错误，继续处理
                                console.warn('JSON解析错误:', e, '原始数据:', line);
                            }
                        }
                    }
                    
                    // 继续读取下一个块
                    return readChunk();
                });
            }
            
            return readChunk();
        })
        .catch(error => {
            console.error('Stream error:', error);
            
            // 更友好的错误提示
            let errorMessage = '网络连接错误，请检查网络连接后重试。';
            if (error.message.includes('HTTP 429')) {
                errorMessage = '请求过于频繁，请稍后再试。';
            } else if (error.message.includes('HTTP 500')) {
                errorMessage = '服务器内部错误，请稍后重试。';
            } else if (error.message.includes('HTTP 401')) {
                errorMessage = 'API密钥无效，请检查配置。';
            }
            
            streamingContent.innerHTML = renderMarkdown(errorMessage);
            assistantMessageDiv.classList.add('error-message');
            
            // 错误动画
            assistantMessageDiv.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
                assistantMessageDiv.style.animation = '';
            }, 500);
        });
    }

    // 更新流式内容 - 处理增量更新
    function updateStreamingContent(newHtml, container) {
        // 如果容器为空，直接设置内容
        if (!container.children.length) {
            container.innerHTML = newHtml;
            return;
        }
        
        // 获取当前HTML内容
        const currentHtml = container.innerHTML;
        
        // 如果内容相同，直接返回
        if (currentHtml === newHtml) {
            return;
        }
        
        // 比较新旧内容，找出新增的部分
        const diff = findHtmlDifference(currentHtml, newHtml);
        
        if (diff.type === 'append') {
            // 追加新内容
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = diff.newContent;
            
            // 将新内容添加到容器末尾
            while (tempDiv.firstChild) {
                container.appendChild(tempDiv.firstChild);
            }
            
            // 为新内容添加动画
            animateNewContent(container, diff.newContent);
        } else {
            // 完全不同的内容，重新设置
            container.innerHTML = newHtml;
        }
    }

    // 查找HTML差异
    function findHtmlDifference(oldHtml, newHtml) {
        // 简单的差异检测：如果新HTML以旧HTML开头，认为是追加
        if (newHtml.startsWith(oldHtml) && newHtml.length > oldHtml.length) {
            return {
                type: 'append',
                newContent: newHtml.substring(oldHtml.length)
            };
        }
        
        return {
            type: 'replace',
            newContent: newHtml
        };
    }

    // 为新内容添加动画
    function animateNewContent(container, newContent) {
        // 获取容器中最后添加的元素
        const lastChild = container.lastElementChild;
        
        if (lastChild && lastChild.nodeType === Node.ELEMENT_NODE) {
            // 如果是元素节点，为其添加淡入动画
            lastChild.style.opacity = '0';
            lastChild.style.transform = 'translateY(10px)';
            lastChild.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            
            requestAnimationFrame(() => {
                lastChild.style.opacity = '1';
                lastChild.style.transform = 'translateY(0)';
            });
        } else if (lastChild && lastChild.nodeType === Node.TEXT_NODE) {
            // 如果是文本节点，添加打字机动画
            const text = lastChild.textContent;
            if (text && text.trim()) {
                animateNewText(lastChild, text);
            }
        }
    }

    // 优化的流式内容动画显示 - 简化版本
    function animateStreamingContent(text, container) {
        // 如果容器为空，直接设置内容
        if (!container.children.length) {
            container.innerHTML = text;
            return;
        }
        
        // 获取当前文本内容（去除HTML标签）
        const currentText = container.textContent || container.innerText || '';
        
        // 如果新文本是当前文本的子串，说明是增量更新
        if (text.startsWith(currentText) && text.length > currentText.length) {
            // 只添加新内容
            const newContent = text.substring(currentText.length);
            
            // 创建新的文本节点
            const textNode = document.createTextNode(newContent);
            container.appendChild(textNode);
            
            // 为新内容添加动画
            animateNewText(textNode, newContent);
        } else {
            // 完全不同的内容，重新设置
            container.innerHTML = text;
        }
    }

    // 为新文本添加动画
    function animateNewText(textNode, text) {
        // 创建动画包装器
        const wrapper = document.createElement('span');
        wrapper.className = 'typing-text';
        
        // 替换文本节点
        textNode.parentNode.insertBefore(wrapper, textNode);
        textNode.parentNode.removeChild(textNode);
        
        // 添加打字机动画
        let charIndex = 0;
        const showNextChar = () => {
            if (charIndex < text.length) {
                const char = text[charIndex];
                wrapper.textContent += char;
                charIndex++;
                
                // 根据字符类型调整延迟
                let delay = 8;
                if (char === ' ' || char === '\n') {
                    delay = 3;
                } else if (char === '.' || char === '!' || char === '?') {
                    delay = 20;
                } else if (char === ',' || char === ';' || char === ':') {
                    delay = 15;
                }
                
                requestAnimationFrame(() => {
                    setTimeout(showNextChar, delay);
                });
            } else {
                // 动画完成，移除动画类
                wrapper.classList.remove('typing-text');
            }
        };
        showNextChar();
    }

    // 辅助函数：将长文本分割成段落
    function splitTextIntoSegments(text) {
        const segments = [];
        const maxSegmentLength = 50;
        
        // 按句子分割
        const sentences = text.split(/([.!?]+)/);
        let currentSegment = '';
        
        for (let i = 0; i < sentences.length; i++) {
            const sentence = sentences[i];
            if (currentSegment.length + sentence.length <= maxSegmentLength) {
                currentSegment += sentence;
            } else {
                if (currentSegment) {
                    segments.push(currentSegment);
                }
                currentSegment = sentence;
            }
        }
        
        if (currentSegment) {
            segments.push(currentSegment);
        }
        
        return segments;
    }

    // 将sendMessage函数暴露为全局函数，以便其他脚本调用
    window.sendMessage = sendMessage;

    // 添加消息到聊天区域
    function addMessage(content, sender, isError = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        if (isError) messageDiv.classList.add('error-message');
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = sender === 'user' ? '👤' : '🤖';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        // 处理消息内容格式
        if (typeof content === 'string') {
            // 应用Markdown渲染
            let formattedContent = renderMarkdown(content);
            formattedContent = postProcessMarkdown(formattedContent);
            messageContent.innerHTML = formattedContent;
        } else {
            messageContent.textContent = content;
        }
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        chatMessages.appendChild(messageDiv);
        
        // 滚动到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        return messageDiv;
    }

    // 添加加载消息
    function addLoadingMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant loading-message';
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = '🤖';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = `
            <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
            </div>
        `;
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
        
        return messageDiv;
    }

    // 移除加载消息
    function removeLoadingMessage(loadingMessage) {
        if (loadingMessage && loadingMessage.parentNode) {
            loadingMessage.parentNode.removeChild(loadingMessage);
        }
    }

    // 绑定事件
    sendButton.addEventListener('click', () => sendMessage());
    
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // 加载历史记录并自动聚焦
    loadChatHistory().then(() => {
        messageInput.focus();
    });

    // --- PDF渲染逻辑 ---
    let pdfDoc = null;
    let pageNum = 1;
    let pageRendering = false;
    let pageNumPending = null;
    let scaleMultiplier = 1.0;
    const viewer = document.getElementById('pdf-viewer');

    function renderPage(num) {
        pageRendering = true;
        
        viewer.innerHTML = '';
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const textLayer = document.createElement('div');
        textLayer.className = 'textLayer';
        viewer.appendChild(canvas);
        viewer.appendChild(textLayer);
        
        pdfDoc.getPage(num).then(function(page) {
            const container = document.getElementById('pdf-viewer');
            if (!container) return;
            const containerWidth = container.clientWidth;
            const containerHeight = container.clientHeight;

            const unscaledViewport = page.getViewport({ scale: 1 });
            
            const scaleX = containerWidth / (unscaledViewport.width + 2);
            const scaleY = containerHeight / (unscaledViewport.height + 2);
            const baseScale = Math.min(scaleX, scaleY);
            
            const scale = baseScale * scaleMultiplier;
            const viewport = page.getViewport({ scale: scale });

            const devicePixelRatio = window.devicePixelRatio || 1;

            canvas.width = Math.floor(viewport.width * devicePixelRatio);
            canvas.height = Math.floor(viewport.height * devicePixelRatio);

            canvas.style.width = `${Math.floor(viewport.width)}px`;
            canvas.style.height = `${Math.floor(viewport.height)}px`;
            
            textLayer.style.width = canvas.style.width;
            textLayer.style.height = canvas.style.height;
            textLayer.style.left = '0px';
            textLayer.style.top = '0px';
            
            ctx.setTransform(devicePixelRatio, 0, 0, devicePixelRatio, 0, 0);

            const renderContext = {
                canvasContext: ctx,
                viewport: viewport
            };
            
            const renderTask = page.render(renderContext);

            page.getTextContent().then(function(textContent) {
                pdfjsLib.renderTextLayer({
                    textContent: textContent,
                    container: textLayer,
                    viewport: viewport,
                    textDivs: []
                });
            });

            renderTask.promise.then(function() {
                pageRendering = false;
                if (pageNumPending !== null) {
                    renderPage(pageNumPending);
                    pageNumPending = null;
                }
            });
        });

        document.getElementById('page_num').textContent = num;
        document.getElementById('zoomLevel').textContent = `${Math.round(scaleMultiplier * 100)}%`;
    }
    
    function queueRenderPage(num) {
        if (pageRendering) {
            pageNumPending = num;
        } else {
            renderPage(num);
        }
    }

    function onPrevPage() {
        if (pageNum <= 1) return;
        pageNum--;
        queueRenderPage(pageNum);
    }
    document.getElementById('prevPageBtn').addEventListener('click', onPrevPage);

    function onNextPage() {
        if (pageNum >= pdfDoc.numPages) return;
        pageNum++;
        queueRenderPage(pageNum);
    }
    document.getElementById('nextPageBtn').addEventListener('click', onNextPage);

    document.getElementById('zoomInBtn').addEventListener('click', function() {
        if (scaleMultiplier >= 3.0) return;
        scaleMultiplier += 0.2;
        queueRenderPage(pageNum);
    });

    document.getElementById('zoomOutBtn').addEventListener('click', function() {
        if (scaleMultiplier <= 0.4) return;
        scaleMultiplier -= 0.2;
        queueRenderPage(pageNum);
    });
    
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            if (pdfDoc) {
                queueRenderPage(pageNum);
            }
        }, 150);
    });
    
    function initializePdfViewer(url) {
        if (typeof pdfjsLib === 'undefined') {
            console.error("PDF.js is not loaded.");
            return;
        }
        pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.worker.min.js`;

        pdfjsLib.getDocument(url).promise.then(function(pdfDoc_) {
            pdfDoc = pdfDoc_;
            document.getElementById('page_count').textContent = pdfDoc.numPages;
            pageNum = 1; // Reset to first page
            renderPage(pageNum);
        }).catch(function(reason) {
            console.error('Error during PDF loading: ' + reason);
        });
    }

    // Initial PDF load
    initializePdfViewer(pdfUrl);

    // 更新PDF显示
    function updatePdfDisplay(paperId) {
        const newPdfUrl = `/view_pdf/${paperId}`;
        initializePdfViewer(newPdfUrl);
    }

    // 将updatePdfDisplay暴露到全局，以便其他地方调用
    window.updatePdfDisplay = updatePdfDisplay;

    // 平滑滚动到底部
    function smoothScrollToBottom() {
        const targetScrollTop = chatMessages.scrollHeight - chatMessages.clientHeight;
        const currentScrollTop = chatMessages.scrollTop;
        const distance = targetScrollTop - currentScrollTop;
        
        if (Math.abs(distance) > 10) { // 只有当滚动距离足够大时才执行
            const duration = 300;
            const startTime = performance.now();
            
            function animate(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // 使用缓动函数
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                
                chatMessages.scrollTop = currentScrollTop + (distance * easeOutQuart);
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            }
            
            requestAnimationFrame(animate);
        }
    }

    // 优化的消息添加函数
    function addMessageWithAnimation(content, sender, isError = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        if (isError) messageDiv.classList.add('error-message');
        
        // 初始状态
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = 'translateY(20px)';
        messageDiv.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.textContent = sender === 'user' ? '👤' : '🤖';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        // 处理消息内容格式
        if (typeof content === 'string') {
            let formattedContent = renderMarkdown(content);
            formattedContent = postProcessMarkdown(formattedContent);
            messageContent.innerHTML = formattedContent;
        } else {
            messageContent.textContent = content;
        }
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);
        
        chatMessages.appendChild(messageDiv);
        
        // 触发动画
        requestAnimationFrame(() => {
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateY(0)';
        });
        
        // 滚动到底部
        smoothScrollToBottom();
        
        return messageDiv;
    }

    // 智能的文本分割函数
    function smartTextSplit(text) {
        // 按句子分割，但保持标点符号
        const sentences = text.split(/([.!?]+)/);
        const segments = [];
        let currentSegment = '';
        
        for (let i = 0; i < sentences.length; i++) {
            const sentence = sentences[i];
            if (currentSegment.length + sentence.length <= 80) {
                currentSegment += sentence;
            } else {
                if (currentSegment) {
                    segments.push(currentSegment);
                }
                currentSegment = sentence;
            }
        }
        
        if (currentSegment) {
            segments.push(currentSegment);
        }
        
        return segments;
    }

    // 性能优化的防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 优化的滚动处理
    const debouncedScroll = debounce(() => {
        smoothScrollToBottom();
    }, 50);

    // 添加键盘快捷键支持
    document.addEventListener('keydown', function(e) {
        // Ctrl+Enter 发送消息
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            sendMessage();
        }
        
        // Esc 键清空输入框
        if (e.key === 'Escape') {
            messageInput.value = '';
            messageInput.blur();
        }
    });

    // 输入框自动调整高度
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
    });

    // 添加输入提示动画
    messageInput.addEventListener('focus', function() {
        this.parentElement.style.transform = 'scale(1.02)';
        this.parentElement.style.boxShadow = '0 4px 20px rgba(123, 179, 217, 0.2)';
    });

    messageInput.addEventListener('blur', function() {
        this.parentElement.style.transform = 'scale(1)';
        this.parentElement.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
    });

    // 初始化
    loadChatHistory();
    initializePdfViewer(pdfUrl);
});

// 添加建议问题
function addSuggestedQuestions() {
    const welcomeMessage = document.querySelector('.welcome-message');
    if (welcomeMessage) {
        const suggestedQuestions = [
            "这篇论文的主要贡献是什么？",
            "请解释论文的核心方法",
            "这篇论文有哪些创新点？",
            "论文的实验结果如何？"
        ];
        
        const questionsContainer = document.createElement('div');
        questionsContainer.className = 'suggested-questions';
        questionsContainer.innerHTML = '<p class="questions-title">💡 建议问题：</p>';
        
        suggestedQuestions.forEach(question => {
            const questionBtn = document.createElement('button');
            questionBtn.className = 'question-suggestion';
            questionBtn.textContent = question;
            questionBtn.onclick = () => {
                document.getElementById('messageInput').value = question;
                document.getElementById('messageInput').focus();
            };
            questionsContainer.appendChild(questionBtn);
        });
        
        welcomeMessage.appendChild(questionsContainer);
    }
}

// 返回论文库
function goBack() {
    window.location.href = '/';
}

// PDF缩放功能
let currentZoom = 100;
const minZoom = 50;
const maxZoom = 200;

document.addEventListener('DOMContentLoaded', function() {
    const zoomInBtn = document.getElementById('zoomInBtn');
    const zoomOutBtn = document.getElementById('zoomOutBtn');
    const zoomLevel = document.getElementById('zoomLevel');
    const pdfViewer = document.getElementById('pdfViewer');
    
    // 缩放功能
    function updateZoom() {
        if (pdfViewer) {
            pdfViewer.style.transform = `scale(${currentZoom / 100})`;
            pdfViewer.style.transformOrigin = 'top left';
        }
        if (zoomLevel) {
            zoomLevel.textContent = `${currentZoom}%`;
        }
    }
    
    // 放大
    if (zoomInBtn) {
        zoomInBtn.addEventListener('click', function() {
            if (currentZoom < maxZoom) {
                currentZoom += 10;
                updateZoom();
            }
        });
    }
    
    // 缩小
    if (zoomOutBtn) {
        zoomOutBtn.addEventListener('click', function() {
            if (currentZoom > minZoom) {
                currentZoom -= 10;
                updateZoom();
            }
        });
    }
    
    // 初始化缩放
    updateZoom();
    
});

// 全局函数：跳转到预阅读页面（修复预阅读按钮问题）
function goToPreRead(paperId) {
    window.location.href = `/view_pre_read/${paperId}`;
}

// 全局函数：跳转到多Agent论文分析页面
function goToMultiAgent(paperId) {
    window.location.href = `/multi_agent_chat/${paperId}`;
}

// 全局函数：返回论文库
function goBack() {
    window.location.href = '/';
}

// 全局函数：发送建议问题
function sendSuggestedQuestion(question) {
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        messageInput.value = question;
        messageInput.focus();
        // 触发发送消息
        if (window.sendMessage) {
            window.sendMessage();
        }
    }
}

// 跨文档分析功能
let multiDocMode = false;
let papersData = []; // 存储所有论文数据
let selectedPapers = [];
let activePapers = []; // 当前激活的跨文档分析中的参考论文ID
let allPapers = [];

// 打开跨文档选择弹窗
function openMultiDocModal() {
    const modal = document.getElementById('multiDocModal');
    if (modal) {
        modal.style.display = 'flex';
        // 重置按钮状态
        const startButton = document.querySelector('.modal-btn.primary');
        if (startButton) {
            startButton.textContent = '开始分析';
            startButton.disabled = selectedPapers.length === 0;
        }
        loadPaperList();
    }
}

// 关闭跨文档选择弹窗
function closeMultiDocModal() {
    const modal = document.getElementById('multiDocModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

// 加载论文列表
async function loadPaperList() {
    try {
        const response = await fetch('/api/papers');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                papersData = data.papers; // 保存论文数据
                allPapers = data.papers; // 兼容现有代码
                renderPaperList(data.papers);
            } else {
                console.error('Failed to load papers:', data.error);
            }
        } else {
            console.error('Failed to load papers');
        }
    } catch (error) {
        console.error('Error loading papers:', error);
    }
}

// 渲染论文列表
function renderPaperList(papers) {
    const paperList = document.getElementById('paperList');
    if (!paperList) return;

    paperList.innerHTML = '';
    
    // 过滤掉当前论文
    const filteredPapers = papers.filter(paper => paper.id !== paperId);
    
    if (filteredPapers.length === 0) {
        paperList.innerHTML = '<p style="text-align: center; color: #6b7280; font-size: 14px;">暂无其他论文可选择</p>';
        return;
    }

    filteredPapers.forEach(paper => {
        const paperItem = document.createElement('div');
        paperItem.className = 'paper-item';
        paperItem.dataset.paperId = paper.id; // 添加data属性存储论文ID
        paperItem.onclick = () => togglePaperSelection(paper.id);
        
        // 检查是否已经在多论文对话中
        const isAlreadyInAnalysis = multiDocMode && activePapers.includes(paper.id);
        const isSelected = selectedPapers.includes(paper.id);
        
        // 检查预阅读状态
        let prereadStatus = '';
        if (paper.has_formal_preread) {
            prereadStatus = '<span class="preread-status has-formal">📚 正式预阅读</span>';
        } else if (paper.has_hidden_preread) {
            prereadStatus = '<span class="preread-status has-hidden">📄 简化预阅读</span>';
        } else {
            prereadStatus = '<span class="preread-status">⏳ 待生成</span>';
        }

        // 如果已经在对话中，添加禁用状态
        if (isAlreadyInAnalysis) {
            paperItem.classList.add('disabled');
            paperItem.style.opacity = '0.5';
            paperItem.style.pointerEvents = 'none';
        }

        paperItem.innerHTML = `
            <input type="checkbox" class="paper-checkbox" ${isSelected ? 'checked' : ''} ${isAlreadyInAnalysis ? 'disabled' : ''}>
            <div class="paper-item-content">
                <div class="paper-item-title">${paper.title}</div>
                <div class="paper-item-meta">
                    ${paper.authors} • ${paper.source} ${prereadStatus}
                    ${isAlreadyInAnalysis ? '<span class="already-added">✓ 已在对话中</span>' : ''}
                </div>
            </div>
        `;
        
        paperList.appendChild(paperItem);
    });
}

// 切换论文选择状态
function togglePaperSelection(paperId) {
    // 检查是否已经在多论文对话中包含了这篇论文
    if (multiDocMode && activePapers.includes(paperId)) {
        alert('这篇论文已经在对话中了，不能重复添加');
        return;
    }
    
    const index = selectedPapers.indexOf(paperId);
    if (index === -1) {
        selectedPapers.push(paperId);
    } else {
        selectedPapers.splice(index, 1);
    }
    
    // 更新UI
    const paperItems = document.querySelectorAll('.paper-item');
    paperItems.forEach(item => {
        const checkbox = item.querySelector('.paper-checkbox');
        const itemPaperId = parseInt(item.dataset.paperId);
        
        if (itemPaperId && selectedPapers.includes(itemPaperId)) {
            item.classList.add('selected');
            checkbox.checked = true;
        } else {
            item.classList.remove('selected');
            checkbox.checked = false;
        }
        
        // 如果论文已经在多论文对话中，禁用选择
        if (multiDocMode && activePapers.includes(itemPaperId)) {
            item.classList.add('disabled');
            item.style.opacity = '0.5';
            item.style.pointerEvents = 'none';
            checkbox.disabled = true;
        } else {
            item.classList.remove('disabled');
            item.style.opacity = '1';
            item.style.pointerEvents = 'auto';
            checkbox.disabled = false;
        }
    });
    
    // 更新开始分析按钮状态
    const startButton = document.querySelector('.modal-btn.primary');
    if (startButton) {
        startButton.disabled = selectedPapers.length === 0;
        // 确保按钮文本正确
        if (startButton.textContent === '正在初始化...') {
            startButton.textContent = '开始分析';
        }
    }
}

// 开始跨文档分析
async function startMultiDocAnalysis() {
    if (selectedPapers.length === 0) {
        alert('请至少选择一篇论文');
        return;
    }

    try {
        // 显示加载状态
        const startButton = document.querySelector('.modal-btn.primary');
        const originalText = startButton.textContent;
        startButton.textContent = '正在添加...';
        startButton.disabled = true;

        // 调用后端API添加论文到对话
        const response = await fetch('/api/start_multi_doc_analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                main_paper_id: paperId,
                reference_paper_ids: selectedPapers
            })
        });

        if (response.ok) {
            const result = await response.json();
            
            // 激活跨文档模式（如果还没有激活）
            if (!multiDocMode) {
                multiDocMode = true;
                activePapers = []; // 初始化空数组
                currentPaperId = paperId; // 初始论文作为当前论文
            }
            
            // 添加新选中的论文到现有列表中（避免重复）
            selectedPapers.forEach(paperId => {
                if (!activePapers.includes(paperId)) {
                    activePapers.push(paperId);
                }
            });
            
            // 显示跨文档状态指示
            const statusDiv = document.getElementById('multiDocStatus');
            if (statusDiv) {
                statusDiv.style.display = 'flex';
                statusDiv.querySelector('.status-text').textContent = 
                    `多论文对话模式 (${activePapers.length + 1}篇论文)`;
            }
            
            // 显示参考论文卡片
            displayReferencePaperCards(activePapers);
            
            // 更新聊天界面提示
            updateChatInterface();
            
            // 关闭弹窗
            closeMultiDocModal();
            
            // 重置选择（用于下次选择）
            selectedPapers = [];
            
        } else {
            const error = await response.json();
            alert('添加失败：' + (error.error || '未知错误'));
        }
    } catch (error) {
        console.error('Error adding papers to conversation:', error);
        alert('添加失败，请稍后重试');
    } finally {
        // 恢复按钮状态
        const startButton = document.querySelector('.modal-btn.primary');
        if (startButton) {
            startButton.textContent = '添加论文';
            startButton.disabled = false;
        }
    }
}

// 显示论文卡片
function displayReferencePaperCards(paperIds) {
    const papersList = document.getElementById('referencePapersList');
    
    if (!papersList) return;
    
    // 清空现有内容
    papersList.innerHTML = '';
    
    if (paperIds.length === 0) {
        return;
    }
    
    // 根据paperIds顺序显示论文卡片
    // 注意：这里的paperIds只包含参考论文，当前论文不在这个列表中
    paperIds.forEach((paperId, index) => {
        const paper = papersData.find(p => p.id === paperId);
        if (paper) {
            const cardElement = createReferencePaperCard(paper, index);
            papersList.appendChild(cardElement);
        }
    });
}

// 创建论文卡片
function createReferencePaperCard(paper, index) {
    const card = document.createElement('div');
    card.className = `paper-info ${paper.id === currentPaperId ? 'current-paper' : ''}`;
    card.dataset.paperId = paper.id;
    
    // 检查论文是否有PDF
    const hasPdf = paper.has_pdf !== false; // 默认为true，除非明确设置为false
    
    // 检查是否为当前论文
    const isCurrentPaper = paper.id === currentPaperId;
    const currentPaperTitle = isCurrentPaper ? '当前论文' : '参考论文';
    
    card.innerHTML = `
        <button class="reference-paper-card-remove" onclick="removeReferencePaper(${paper.id})" title="移除此论文">
            ×
        </button>
        <button class="reference-paper-card-pin ${isCurrentPaper ? 'pinned' : ''}" onclick="switchCurrentPaper(${paper.id})" title="${isCurrentPaper ? '当前论文' : '设为当前论文'}">
            📌
        </button>
        <h3 class="info-title ${isCurrentPaper ? 'current-paper' : ''}">${currentPaperTitle}</h3>
        <div class="paper-details">
            <p class="paper-title-sidebar">${paper.title}</p>
            <p class="paper-authors-sidebar">${paper.authors}</p>
            <p class="paper-source-sidebar">${paper.source}</p>
        </div>
        
        <!-- 集成的预阅读功能 -->
        ${hasPdf ? `
        <div class="integrated-preread">
            <div class="preread-actions">
                <button class="preread-btn" onclick="goToPreRead(${paper.id})">
                    <span class="btn-icon">📚</span>
                    查看预阅读
                </button>
            </div>
            <p class="preread-hint">AI深度分析论文内容</p>
        </div>
        ` : ''}
    `;
    
    return card;
}

// 移除论文
function removeReferencePaper(paperId) {
    if (!multiDocMode) return;
    
    // 从激活的论文列表中移除
    const index = activePapers.indexOf(paperId);
    if (index > -1) {
        activePapers.splice(index, 1);
    }
    
    // 如果移除的是当前论文，需要切换到其他论文
    if (paperId === currentPaperId) {
        if (activePapers.length > 0) {
            // 切换到第一篇可用的论文
            switchCurrentPaper(activePapers[0]);
        } else {
            // 如果没有其他论文了，退出多论文模式
            exitMultiDocMode();
            return;
        }
    }
    
    // 更新显示
    displayReferencePaperCards(activePapers);
    
    // 更新状态指示
    const statusDiv = document.getElementById('multiDocStatus');
    if (statusDiv) {
        if (activePapers.length === 0) {
            // 如果没有其他论文了，退出多论文模式
            exitMultiDocMode();
        } else {
            statusDiv.querySelector('.status-text').textContent = 
                `多论文对话模式 (${activePapers.length + 1}篇论文)`;
        }
    }
    
    // 重新渲染论文列表，使被移除的论文可以重新选择
    if (papersData.length > 0) {
        renderPaperList(papersData);
    }
}

// 切换当前论文
async function switchCurrentPaper(paperId) {
    if (paperId === currentPaperId) {
        return; // 已经是当前论文，无需切换
    }
    
    try {
        // 显示加载状态
        const pinButton = document.querySelector(`[onclick="switchCurrentPaper(${paperId})"]`);
        if (pinButton) {
            pinButton.style.opacity = '0.5';
            pinButton.disabled = true;
        }
        
        // 调用后端API切换当前论文
        const response = await fetch('/api/switch_current_paper', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                paper_id: paperId
            })
        });
        
        if (response.ok) {
            const result = await response.json();
            
            // 保存原来的当前论文ID
            const oldCurrentPaperId = currentPaperId;
            
            // 更新当前论文ID
            currentPaperId = paperId;
            
            // 更新activePapers数组：将原来的当前论文添加到参考论文列表，将新的当前论文从参考论文列表中移除
            if (activePapers.includes(paperId)) {
                // 从参考论文列表中移除新的当前论文
                const index = activePapers.indexOf(paperId);
                activePapers.splice(index, 1);
            }
            
            // 将原来的当前论文添加到参考论文列表（如果它不在列表中）
            if (!activePapers.includes(oldCurrentPaperId)) {
                activePapers.push(oldCurrentPaperId);
            }
            
            // 更新PDF显示
            window.updatePdfDisplay(paperId);
            
            // 更新当前论文信息显示
            updateCurrentPaperInfo(paperId);
            
            // 更新论文卡片显示 - 确保正确交换内容
            displayReferencePaperCards(activePapers);
            
            // 更新聊天界面提示
            updateChatInterface();
            
            // 更新状态指示器
            const statusDiv = document.getElementById('multiDocStatus');
            if (statusDiv) {
                statusDiv.querySelector('.status-text').textContent = 
                    `多论文对话模式 (${activePapers.length + 1}篇论文)`;
            }
            
            // 显示成功消息
            showNotification(result.message, 'success');
            
        } else {
            const error = await response.json();
            showNotification('切换失败：' + (error.error || '未知错误'), 'error');
        }
    } catch (error) {
        console.error('Error switching current paper:', error);
        showNotification('切换失败，请稍后重试', 'error');
    } finally {
        // 恢复按钮状态
        const pinButton = document.querySelector(`[onclick="switchCurrentPaper(${paperId})"]`);
        if (pinButton) {
            pinButton.style.opacity = '1';
            pinButton.disabled = false;
        }
    }
}

// 更新当前论文信息显示
function updateCurrentPaperInfo(paperId) {
    const currentPaperInfo = document.getElementById('currentPaperInfo');
    if (!currentPaperInfo) return;
    
    const paper = papersData.find(p => p.id === paperId);
    if (!paper) return;
    
    const titleElement = currentPaperInfo.querySelector('.paper-title-sidebar');
    const authorsElement = currentPaperInfo.querySelector('.paper-authors-sidebar');
    const sourceElement = currentPaperInfo.querySelector('.paper-source-sidebar');
    const prereadBtn = currentPaperInfo.querySelector('.preread-btn');
    
    if (titleElement) titleElement.textContent = paper.title;
    if (authorsElement) authorsElement.textContent = paper.authors;
    if (sourceElement) sourceElement.textContent = paper.source;
    
    // 更新预阅读按钮的onclick事件
    if (prereadBtn) {
        prereadBtn.onclick = () => goToPreRead(paperId);
    }
}

// 显示通知消息
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// 退出多论文模式
function exitMultiDocMode() {
    multiDocMode = false;
    selectedPapers = [];
    activePapers = [];
    currentPaperId = paperId; // 重置为初始论文
    
    // 隐藏状态指示
    const statusDiv = document.getElementById('multiDocStatus');
    if (statusDiv) {
        statusDiv.style.display = 'none';
    }
    
    // 清空论文卡片
    const papersList = document.getElementById('referencePapersList');
    if (papersList) {
        papersList.innerHTML = '';
    }
    
    // 恢复PDF显示
    updatePdfDisplay(paperId);
    
    // 恢复聊天界面
    updateChatInterface();
}

// 更新聊天界面
function updateChatInterface() {
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        if (multiDocMode) {
            messageInput.placeholder = '多论文对话提问...';
            
            // 获取当前论文信息
            const currentPaper = papersData.find(p => p.id === currentPaperId) || 
                                { title: '未知论文', authors: '未知作者' };
            
            // 添加多论文对话的欢迎消息
            const welcomeMessage = document.querySelector('.welcome-message');
            if (welcomeMessage) {
                const assistantMessage = welcomeMessage.querySelector('.message.assistant .message-content');
                if (assistantMessage) {
                    assistantMessage.innerHTML = `
                        您好！我现在可以进行多论文对话了。当前以 <strong>${currentPaper.title}</strong> 为主要视角，结合其他参考论文来回答您的问题。您可以询问关于这些论文的对比分析问题，比如：
                        
                        <div class="suggested-questions">
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('请以当前论文为主要视角，对比这些论文的主要方法有什么异同？')">
                                请以当前论文为主要视角，对比这些论文的主要方法有什么异同？
                            </button>
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('当前论文相比其他论文有什么独特之处？')">
                                当前论文相比其他论文有什么独特之处？
                            </button>
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('这些研究之间有什么关联性？')">
                                这些研究之间有什么关联性？
                            </button>
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('综合这些论文，该领域的发展趋势是什么？')">
                                综合这些论文，该领域的发展趋势是什么？
                            </button>
                        </div>
                    `;
                }
            }
        } else {
            messageInput.placeholder = '向论文提问...';
            
            // 恢复原始欢迎消息
            const welcomeMessage = document.querySelector('.welcome-message');
            if (welcomeMessage) {
                const assistantMessage = welcomeMessage.querySelector('.message.assistant .message-content');
                if (assistantMessage) {
                    assistantMessage.innerHTML = `
                        您好！我是您的论文阅读助手。我已经了解了这篇论文的内容，您可以向我询问关于这篇论文的任何问题，比如：
                        
                        <div class="suggested-questions">
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('这篇论文的主要贡献是什么？')">
                                这篇论文的主要贡献是什么？
                            </button>
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('请解释一下论文中的核心方法')">
                                请解释一下论文中的核心方法
                            </button>
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('这篇论文有哪些局限性？')">
                                这篇论文有哪些局限性？
                            </button>
                            <button class="question-suggestion" onclick="sendSuggestedQuestion('相关工作有哪些？')">
                                相关工作有哪些？
                            </button>
                        </div>
                    `;
                }
            }
        }
    }
}