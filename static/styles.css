:root {
    --primary-color: #d1778f;
    --secondary-color: #b96a7e;
    --accent-color: #f09393;
    --gradient-1: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-2: linear-gradient(135deg, var(--accent-color) 0%, #f5576c 100%);
    --dark-color: #333333;
    --text-light: #666666;
    --light-bg: #fdf8f9;
    --border-light: #f0e6e9;
    --success-color: #7bb3d9;
    --success-hover: #6ba5d1;
    --success-active: #5a97c9;
    --blue-gradient: linear-gradient(135deg, #7bb3d9 0%, #6ba5d1 100%);
    --success-bg: #e6f3ff;
    --success-text: #1e5a96;
    --error-color: #dc2626;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    background: linear-gradient(to bottom, #fff5f5 0%, #ffffff 100%);
    color: #333333;
    height: 100vh;
    overflow: hidden;
}

.app-container {
    display: flex;
    height: 100vh;
    background: #ffffff;
}

/* 左侧功能栏 */
.sidebar {
    width: 280px;
    background: #fdf8f9;
    border-right: 1px solid #f0e6e9;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid #f0e6e9;
}

.app-logo {
    text-align: center;
}

.app-name {
    font-size: 24px;
    font-weight: 700;
    color: #333333;
    margin-bottom: 4px;
    background: linear-gradient(135deg, #d1778f 0%, #b96a7e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.app-name-chinese {
    font-size: 14px;
    color: #666666;
    font-weight: 400;
}

.sidebar-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.nav-title {
    font-size: 12px;
    font-weight: 600;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 12px;
    padding: 0 4px;
}

.nav-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 8px;
}

.nav-card:hover, .nav-card.active {
    background: #f3e8eb;
    transform: translateX(4px);
}

.nav-card.active {
    background: #e9dde0;
}

.nav-card-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background: rgba(209, 119, 143, 0.1);
    color: #b96a7e;
}

.nav-card-text {
    flex: 1;
}

.nav-card-title {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
}

.nav-card-desc {
    font-size: 12px;
    color: #6b7280;
    line-height: 1.4;
}

.back-button {
    width: 100%;
    background: #f8f3f4;
    border: 1px solid #f0e6e9;
    border-radius: 8px;
    padding: 12px 16px;
    color: #555555;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
}

.back-button:hover {
    background: #f3e8eb;
    transform: translateY(-1px);
}

.back-icon {
    font-size: 16px;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    background: #ffffff;
    overflow: hidden;
    display: flex;
    flex-direction: row;
}

/* 论文库页面 */
.library-container {
    height: 100vh;
    overflow-y: auto;
    background: #ffffff;
}

.library-header {
    background: #ffffff;
    padding: 40px;
    border-bottom: 1px solid #e2e8f0;
}

.header-content {
    text-align: center;
    margin-bottom: 32px;
}

.library-title {
    font-size: 36px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.title-icon {
    font-size: 36px;
}

.library-subtitle {
    font-size: 16px;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

/* 添加论文区域 */
.add-paper-section {
    margin-top: 32px;
}

.upload-options {
    display: flex;
    gap: 24px;
    justify-content: center;
    flex-wrap: wrap;
}

.upload-option {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.upload-btn {
    background: #d1778f;
    border: none;
    border-radius: 12px;
    padding: 16px 24px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(209, 119, 143, 0.3);
}

.upload-btn:hover {
    background: #b96a7e;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(209, 119, 143, 0.4);
}

.btn-icon {
    font-size: 18px;
}

.arxiv-input-container {
    display: flex;
    gap: 12px;
    align-items: center;
}

.arxiv-input {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: #ffffff;
    color: #1f2937;
    font-size: 14px;
    min-width: 250px;
    transition: border-color 0.2s ease;
}

.arxiv-input:focus {
    outline: none;
    border-color: #d1778f;
    box-shadow: 0 0 0 3px rgba(209, 119, 143, 0.1);
}

.arxiv-input::placeholder {
    color: #9ca3af;
}

.add-arxiv-btn {
    background: var(--success-color);
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
}

.add-arxiv-btn:hover {
    background: var(--success-hover);
    transform: scale(1.05);
}

/* 论文卡片网格 */
.papers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
    padding: 40px;
}

.paper-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 16px 20px 20px 20px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    height: auto;
    opacity: 1;
    transform: translateY(0);
}

.paper-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-color: #d1d5db;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.card-header-right {
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
}

.paper-source {
    background: #f3f4f6;
    color: #6b7280;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
}

.arxiv-id-badge {
    background-color: #ffedd5;
    color: #9a3412;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 11px;
}

.top-row {
    display: flex;
    align-items: center;
    gap: 4px;
}

.pdf-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
}

.pdf-available {
    background: var(--success-bg);
    color: var(--success-text);
}

.pdf-unavailable {
    background: #fef2f2;
    color: #dc2626;
}

.paper-date-bottom {
    font-size: 11px;
    color: #9ca3af;
    text-align: right;
    margin-top: auto;
    margin-bottom: 8px;
}

.card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
}

.paper-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
    line-height: 1.4;
    max-height: 4.2em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    text-overflow: ellipsis;
}

.paper-authors {
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 12px;
    max-height: 4.2em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    hyphens: auto;
    line-height: 1.4;
    text-overflow: ellipsis;
}

.paper-abstract {
    font-size: 14px;
    color: #4b5563;
    line-height: 1.5;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    max-height: 5.8em;
    word-wrap: break-word;
    hyphens: auto;
    margin-bottom: 12px;
    text-overflow: ellipsis;
}

.card-footer {
    border-top: 1px solid #f3f4f6;
    padding-top: 12px;
}

.card-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.chat-button {
    background: #d1778f;
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    font-weight: 500;
}

.chat-button:hover {
    background: #b96a7e;
    transform: scale(1.05);
}

.chat-icon {
    font-size: 14px;
}

.multi-agent-button {
    background: linear-gradient(135deg, #6f42c1 0%, #563d7c 100%);
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    font-weight: 500;
}

.multi-agent-button:hover {
    background: linear-gradient(135deg, #563d7c 0%, #495057 100%);
    transform: scale(1.05);
}

.multi-agent-icon {
    font-size: 14px;
}

.pre-read-button {
    background: var(--success-hover);
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    font-weight: 500;
}

.pre-read-button:hover {
    background: var(--success-active);
    transform: scale(1.05);
}

.pre-read-icon {
    font-size: 14px;
}

.delete-button {
    background: #ef4444;
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    font-weight: 500;
}

.delete-button:hover {
    background: #dc2626;
    transform: scale(1.05);
}

.delete-icon {
    font-size: 14px;
}

/* 三点菜单样式 */
.card-menu {
    position: relative;
}

.menu-trigger {
    background: transparent;
    border: none;
    border-radius: 6px;
    padding: 6px 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-trigger:hover {
    background: #f3f4f6;
    color: #374151;
}

.dots {
    font-size: 16px;
    font-weight: bold;
    transform: rotate(90deg);
    line-height: 1;
}

.menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    min-width: 120px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.menu-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 10px 14px;
    background: transparent;
    border: none;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 13px;
    text-align: left;
}

.menu-item:hover {
    background: #f8fafc;
}

.menu-item.delete-item {
    color: #dc2626;
}

.menu-item.delete-item:hover {
    background: #fef2f2;
    color: #b91c1c;
}

.menu-icon {
    font-size: 14px;
}

/* 预阅读访问卡片 */
.pre-read-access-card {
    background: var(--blue-gradient);
    border-radius: 12px;
    padding: 16px;
    margin-top: 16px;
    color: white;
}

.pre-read-access-card .info-title {
    color: white;
    margin-bottom: 8px;
}

.pre-read-desc {
    font-size: 13px;
    opacity: 0.9;
    margin-bottom: 12px;
}

.pre-read-access-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
    width: 100%;
    justify-content: center;
}

.pre-read-access-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* 流式消息样式 */
.streaming-content {
    position: relative;
    animation: fadeIn 0.3s ease-out;
    line-height: 1.6;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 优化的闪烁光标 */
.streaming-content::after {
    content: '▋';
    position: relative;
    display: inline-block;
    animation: blink 1.2s infinite;
    color: var(--success-color);
    font-weight: bold;
    text-shadow: 0 0 8px rgba(123, 179, 217, 0.6);
    margin-left: 2px;
    font-size: 1.1em;
}

@keyframes blink {
    0%, 50% { 
        opacity: 1; 
        transform: scale(1);
        text-shadow: 0 0 8px rgba(123, 179, 217, 0.8);
    }
    51%, 100% { 
        opacity: 0.3; 
        transform: scale(0.9);
        text-shadow: 0 0 4px rgba(123, 179, 217, 0.4);
    }
}

/* 当流式响应完成时移除闪烁光标 */
.message.completed .streaming-content::after {
    display: none;
}

/* 优化的打字机文本动画 */
.streaming-content .typing-text {
    display: inline;
    position: relative;
}

/* 打字机效果的光标 */
.streaming-content .typing-text::after {
    content: '|';
    position: relative;
    display: inline-block;
    animation: typeCursor 0.8s infinite;
    color: var(--success-color);
    font-weight: bold;
    margin-left: 1px;
}

@keyframes typeCursor {
    0%, 50% { 
        opacity: 1; 
    }
    51%, 100% { 
        opacity: 0; 
    }
}

/* 当消息完成时移除打字机光标 */
.message.completed .streaming-content .typing-text::after {
    display: none;
}

/* 优化的字符动画 */
.streaming-content .char-animation {
    display: inline-block;
    animation: charAppear 0.05s ease-out;
}

@keyframes charAppear {
    from {
        opacity: 0;
        transform: translateY(2px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 特殊元素的动画效果 */
.streaming-content pre,
.streaming-content code,
.streaming-content ul,
.streaming-content ol {
    animation: slideInUp 0.4s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 代码块和特殊内容的样式优化 */
.streaming-content pre {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    margin: 16px 0;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    position: relative;
}

.streaming-content pre::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--success-color), var(--primary-color));
    border-radius: 12px 12px 0 0;
}

.streaming-content code {
    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
    padding: 3px 8px;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    color: #d73a49;
    border: 1px solid #e1e5e9;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.streaming-content strong {
    font-weight: 600;
    color: #1a202c;
    position: relative;
}

.streaming-content strong::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--success-color), transparent);
    opacity: 0.3;
}

.streaming-content em {
    font-style: italic;
    color: #4a5568;
    background: linear-gradient(135deg, rgba(123, 179, 217, 0.1) 0%, transparent 100%);
    padding: 1px 4px;
    border-radius: 4px;
}

/* 列表样式优化 */
.streaming-content ul,
.streaming-content ol {
    margin: 12px 0;
    padding-left: 24px;
}

.streaming-content li {
    margin: 6px 0;
    position: relative;
    animation: listItemAppear 0.3s ease-out;
}

@keyframes listItemAppear {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 链接样式优化 */
.streaming-content a {
    color: var(--success-color);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;
    position: relative;
}

.streaming-content a:hover {
    border-bottom-color: var(--success-color);
    background: rgba(123, 179, 217, 0.1);
    padding: 2px 4px;
    border-radius: 4px;
}

/* 标题样式优化 */
.streaming-content h1,
.streaming-content h2,
.streaming-content h3 {
    margin: 20px 0 12px 0;
    color: #1a202c;
    position: relative;
    animation: titleSlideIn 0.4s ease-out;
}

@keyframes titleSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.streaming-content h1::before,
.streaming-content h2::before,
.streaming-content h3::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 1.2em;
    background: linear-gradient(135deg, var(--success-color), var(--primary-color));
    border-radius: 2px;
}

/* 引用样式优化 */
.streaming-content blockquote {
    border-left: 4px solid var(--success-color);
    padding: 12px 20px;
    margin: 16px 0;
    background: linear-gradient(135deg, rgba(123, 179, 217, 0.05) 0%, rgba(123, 179, 217, 0.02) 100%);
    border-radius: 0 8px 8px 0;
    font-style: italic;
    color: #4a5568;
    position: relative;
    animation: quoteAppear 0.5s ease-out;
}

@keyframes quoteAppear {
    from {
        opacity: 0;
        transform: translateX(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 24px;
    opacity: 0.5;
}

.empty-title {
    color: #1f2937;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 12px;
}

.empty-message {
    color: #6b7280;
    font-size: 16px;
    max-width: 400px;
    margin: 0 auto;
}

/* 聊天界面 */
.chat-panel {
    width: 50%;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    border-right: 1px solid #e2e8f0;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100%;
    margin: 0;
    padding: 0 24px;
    background: #ffffff;
}

.chat-header {
    padding: 24px 0;
    border-bottom: 1px solid var(--border-light);
    text-align: center;
    width: 100%;
    background: #ffffff;
}

.chat-title {
    font-size: 26px;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 6px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    letter-spacing: -0.02em;
}

.chat-subtitle {
    font-size: 15px;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.5;
    font-weight: 400;
    letter-spacing: 0.01em;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 24px 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
    background: #fafbfc;
}

.message {
    display: flex;
    gap: 16px;
    max-width: 85%;
    margin-bottom: 20px;
    animation: messageSlideIn 0.4s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(15px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.message.user .message-avatar {
    background: var(--gradient-1);
    color: white;
    box-shadow: 0 4px 12px rgba(209, 119, 143, 0.3);
}

.message.assistant .message-avatar {
    background: var(--blue-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(123, 179, 217, 0.3);
}

.message-content {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 18px;
    padding: 16px 20px;
    color: #2d3748;
    line-height: 1.7;
    font-size: 15px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
    letter-spacing: 0.01em;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
}

.message.user .message-content {
    background: var(--gradient-1);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 16px rgba(209, 119, 143, 0.25);
}

.message.assistant.error-message .message-content {
    background: #fef2f2;
    border-color: #fecaca;
    color: #dc2626;
}

/* 输入区域 */
.chat-input-area {
    padding: 24px 0;
    border-top: 1px solid #e2e8f0;
    background: #ffffff;
}

.input-container {
    display: flex;
    gap: 14px;
    align-items: flex-end;
}

.message-input {
    flex: 1;
    padding: 14px 18px;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    background: #ffffff;
    color: #1f2937;
    font-size: 15px;
    line-height: 1.5;
    resize: none;
    min-height: 48px;
    max-height: 120px;
    transition: all 0.2s ease;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
    letter-spacing: 0.01em;
}

.message-input:focus {
    outline: none;
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(123, 179, 217, 0.15);
    transform: translateY(-1px);
}

.message-input::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

.send-button {
    background: var(--success-color);
    border: none;
    border-radius: 14px;
    padding: 14px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    height: 48px;
    box-shadow: 0 3px 10px rgba(123, 179, 217, 0.3);
    font-weight: 500;
}

.send-button:hover {
    background: var(--success-hover);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 16px rgba(123, 179, 217, 0.4);
}

.send-button:active {
    transform: translateY(0) scale(1.02);
    box-shadow: 0 2px 8px rgba(123, 179, 217, 0.3);
}

.send-button:disabled {
    background: #d1d5db;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* PDF面板 */
.pdf-panel {
    flex: 1;
    background: #f8fafc;
    display: flex;
    flex-direction: column;
    padding: 0 20px 20px 20px; /* Add some padding */
    overflow: hidden;
    min-width: 0;
}

.pdf-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
}

.pdf-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.pdf-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.pdf-control-btn {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 6px;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.pdf-control-btn:hover {
    background: #e5e7eb;
}

.zoom-level {
    font-size: 14px;
    color: #6b7280;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

.pdf-viewer-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    min-height: 400px; /* 确保有最小高度 */
}

#pdf-viewer {
    width: 100%;
    height: 100%;
    border: none;
    background: #ffffff;
    transition: transform 0.2s ease;
    position: relative;
    overflow: auto;
}

.textLayer {
  position: absolute;
  text-align: initial;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  line-height: 1;
  z-index: 10;
  user-select: text !important;
}

.textLayer > span {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;
  user-select: text !important;
}

.textLayer ::selection {
  background: rgb(0 0 255 / 30%);
}

.textLayer .highlight.selected {
  background-color: rgb(0 0 255 / 30%);
}

/* Context Menu for Click-to-Ask */
.context-menu {
  position: absolute;
  background: white;
  border: 1px solid #ccc;
  box-shadow: 2px 2px 5px rgba(0,0,0,0.2);
  z-index: 1000;
  border-radius: 4px;
  padding: 4px 0;
  min-width: 120px;
}

.context-menu button {
  display: block;
  width: 100%;
  padding: 8px 12px;
  text-align: left;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.context-menu button:hover {
  background: #f0f0f0;
}

/* 论文信息 */
.paper-info {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 16px;
    margin-top: 16px;
    position: relative;
}

.info-title {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
}

.paper-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.paper-title-sidebar {
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.4;
}

.paper-authors-sidebar {
    font-size: 13px;
    color: #6b7280;
}

.paper-source-sidebar {
    font-size: 12px;
    color: #9ca3af;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: #ffffff;
    border-radius: 12px;
    padding: 32px;
    text-align: center;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #1f2937;
    font-size: 16px;
    font-weight: 500;
}

/* 建议问题 */
.suggested-questions {
    margin-top: 20px;
    display: grid;
    gap: 10px;
}

.questions-title {
    font-size: 15px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
    text-align: center;
}

.question-suggestion {
    background: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 12px 16px;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    text-align: left;
    line-height: 1.5;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 400;
    letter-spacing: 0.01em;
}

.question-suggestion:hover {
    background: #f8fafc;
    color: var(--success-color);
    border-color: var(--success-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(123, 179, 217, 0.15);
}

/* 输入类型指示器 */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
}

.typing-indicator span {
    width: 8px;
    height: 8px;
    background: #9ca3af;
    border-radius: 50%;
    animation: typing 1.4s ease-in-out infinite;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-content {
        flex-direction: column;
    }
    
    .chat-panel,
    .pdf-panel {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .chat-panel {
        height: 60vh;
    }
    
    .pdf-panel {
        height: 40vh;
        border-bottom: none;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 260px;
    }
    
    .papers-grid {
        grid-template-columns: 1fr;
        padding: 20px;
    }
    
    .upload-options {
        flex-direction: column;
        align-items: center;
    }
    
    .arxiv-input-container {
        flex-direction: column;
        width: 100%;
    }
    
    .arxiv-input {
        width: 100%;
    }
    
    .main-content {
        flex-direction: column;
    }
    
    .chat-panel {
        width: 100%;
        height: 70vh;
        border-right: none;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .pdf-panel {
        width: 100%;
        height: 30vh;
        border-bottom: none;
    }
    
    .chat-container {
        padding: 0 15px;
    }
    
    .paper-card {
        padding: 12px 16px 16px 16px;
    }
    
    .paper-title {
        font-size: 16px;
        max-height: 3.2em;
        -webkit-line-clamp: 2;
    }
    
    .paper-abstract {
        max-height: 4.2em;
        -webkit-line-clamp: 3;
    }
    
    .card-actions {
        gap: 8px;
    }
    
    .chat-button,
    .multi-agent-button,
    .pre-read-button {
        padding: 8px 12px;
        font-size: 12px;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 确认对话框 */
.confirm-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.confirm-content {
    background: #ffffff;
    border-radius: 12px;
    padding: 24px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.confirm-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 12px;
}

.confirm-message {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 24px;
    line-height: 1.5;
}

.confirm-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.confirm-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.confirm-btn.danger {
    background: #ef4444;
    color: white;
}

.confirm-btn.danger:hover {
    background: #dc2626;
}

.confirm-btn.cancel {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.confirm-btn.cancel:hover {
    background: #e5e7eb;
}

/* 隐藏元素 */
.hidden {
    display: none !important;
}

/* 特殊样式 */
.chat-switch-btn {
    margin-top: 10px;
    background: var(--success-hover);
}

/* 文本截断通用样式 */
.text-truncate {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    word-wrap: break-word;
    text-overflow: ellipsis;
}

.text-truncate-2 {
    -webkit-line-clamp: 2;
    max-height: 2.8em;
}

.text-truncate-3 {
    -webkit-line-clamp: 3;
    max-height: 3.8em;
}

.text-truncate-4 {
    -webkit-line-clamp: 4;
    max-height: 5.8em;
}

.empty-icon-large {
    font-size: 60px;
    margin-bottom: 20px;
}

.pre-read-chat-container {
    max-width: 100%;
    margin: 0;
}

.pre-read-page-container {
    height: calc(100vh - 40px);
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.pre-read-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin: 20px 0;
    line-height: 1.6;
    max-height: calc(100vh - 200px);
    width: 100%;
    max-width: 1000px;
}

.pre-read-content h1 { 
    color: var(--primary-color); 
    margin-top: 0; 
}

.pre-read-content h2 { 
    color: var(--secondary-color); 
    border-bottom: 2px solid #f0e6e9; 
    padding-bottom: 8px; 
}

.pre-read-content h3 { 
    color: var(--accent-color); 
}

.pre-read-content ul, 
.pre-read-content ol { 
    padding-left: 24px; 
}

.pre-read-content li { 
    margin: 8px 0; 
}

.pre-read-content blockquote {
    border-left: 4px solid var(--border-light);
    padding-left: 16px;
    margin: 16px 0;
    color: var(--text-light);
}

.pre-read-content code {
    background: var(--light-bg);
    padding: 2px 4px;
    border-radius: 4px;
}

/* 修复预阅读页面滚动问题的通用样式 */
.chat-container.pre-read-chat-container {
    height: auto !important;
    max-height: 100vh;
    overflow-y: auto;
}

/* 确保预阅读内容可以滚动 */
#preReadContent {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin: 20px 0;
    line-height: 1.6;
}

/* 空状态样式 */
.empty-pre-read {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-light);
    width: 100%;
    max-width: 600px;
}

.generate-pre-read-btn {
    background: var(--gradient-1);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    margin-top: 20px;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(209, 119, 143, 0.3);
}

.generate-pre-read-btn:hover {
    background: var(--gradient-2);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(209, 119, 143, 0.4);
}

.generate-pre-read-btn:hover {
    transform: translateY(-2px);
}

/* 集成的预阅读样式 */
.integrated-preread {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e2e8f0;
}

.preread-actions {
    margin-bottom: 6px;
}

.preread-btn {
    background: var(--gradient-1);
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
    width: 100%;
    justify-content: center;
}

.preread-btn:hover {
    background: var(--gradient-2);
    transform: translateY(-1px);
}

.preread-hint {
    font-size: 11px;
    color: #6b7280;
    text-align: center;
    margin: 0;
}

/* 跨文档分析按钮样式 */
.multi-doc-section {
    margin-top: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
    border: 1px solid #f9a8d4;
    border-radius: 12px;
}

.multi-doc-button {
    background: var(--gradient-1);
    border: none;
    border-radius: 10px;
    padding: 12px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    width: 100%;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(209, 119, 143, 0.25);
}

.multi-doc-button:hover {
    background: var(--gradient-2);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(209, 119, 143, 0.35);
}

.multi-doc-icon {
    font-size: 16px;
}

.multi-doc-desc {
    font-size: 12px;
    color: var(--secondary-color);
    text-align: center;
    margin: 8px 0 0 0;
    opacity: 0.8;
}

/* 多Agent分析按钮样式 */
.multi-agent-section {
    margin-top: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 1px solid #7bb3d9;
    border-radius: 12px;
}

.multi-agent-section .multi-agent-button {
    background: var(--blue-gradient);
    border: none;
    border-radius: 10px;
    padding: 12px 16px;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    width: 100%;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(123, 179, 217, 0.25);
}

.multi-agent-section .multi-agent-button:hover {
    background: linear-gradient(135deg, #6ba5d1 0%, #5a97c9 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(123, 179, 217, 0.35);
}

.multi-agent-section .multi-agent-icon {
    font-size: 16px;
}

.multi-agent-desc {
    font-size: 12px;
    color: var(--success-text);
    text-align: center;
    margin: 8px 0 0 0;
    opacity: 0.8;
}

/* 多Agent控制面板样式 */
.multi-agent-controls {
    margin-top: 20px;
    padding: 16px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #f0e6e9;
}

.control-title {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    margin: 0 0 16px 0;
}

.control-group {
    margin-bottom: 16px;
}

.control-label {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #666666;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.control-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #f0e6e9;
    border-radius: 8px;
    font-size: 14px;
    background: #ffffff;
    color: #333333;
}

.control-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(209, 119, 143, 0.1);
}

.agent-toggles {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.agent-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 6px 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.agent-toggle:hover {
    background-color: #fdf8f9;
}

.agent-toggle input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

.toggle-label {
    font-size: 14px;
    color: #333333;
}

.output-options {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.output-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 6px 8px;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.output-option:hover {
    background-color: #fdf8f9;
}

.output-option input[type="radio"] {
    width: 14px;
    height: 14px;
    accent-color: var(--primary-color);
}

.option-label {
    font-size: 14px;
    color: #333333;
}

/* 多Agent状态指示器 */
.multi-agent-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px 12px;
    background: linear-gradient(135deg, #7bb3d9 0%, #6ba5d1 100%);
    border-radius: 8px;
    color: white;
    font-size: 12px;
    font-weight: 500;
}

.status-icon {
    font-size: 14px;
}

/* Agent介绍卡片 */
.agent-introduction {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin: 20px 0;
}

.agent-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #fdf8f9;
    border-radius: 8px;
    border: 1px solid #f0e6e9;
}

.agent-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #f0e6e9;
}

.agent-info h4 {
    font-size: 14px;
    font-weight: 600;
    color: #333333;
    margin: 0 0 4px 0;
}

.agent-info p {
    font-size: 12px;
    color: #666666;
    margin: 0;
    line-height: 1.4;
}

/* 加载动画 */
.loading-dots {
    display: flex;
    gap: 4px;
    margin-bottom: 8px;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: loading-dots 1.4s infinite ease-in-out;
}

.loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes loading-dots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .agent-introduction {
        grid-template-columns: 1fr;
    }
    
    .agent-card {
        flex-direction: column;
        text-align: center;
    }
}

/* 跨文档分析状态指示 */
.multi-doc-status {
    display: flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
    border: 1px solid var(--primary-color);
    border-radius: 8px;
    padding: 8px 12px;
    margin-top: 8px;
    font-size: 12px;
}

.status-icon {
    color: var(--primary-color);
}

.status-text {
    color: var(--secondary-color);
    font-weight: 500;
    flex: 1;
}

.exit-multi-doc {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 4px;
    padding: 4px 8px;
    color: #dc2626;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s ease;
}

.exit-multi-doc:hover {
    background: rgba(239, 68, 68, 0.2);
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: #ffffff;
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-desc {
    color: #6b7280;
    margin-bottom: 20px;
    font-size: 14px;
}

.paper-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.paper-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.paper-item:hover {
    background: #f9fafb;
    border-color: #d1d5db;
}

.paper-item.selected {
    background: #eff6ff;
    border-color: #3b82f6;
}

.paper-checkbox {
    margin: 0;
    cursor: pointer;
}

.paper-item-content {
    flex: 1;
}

.paper-item-title {
    font-size: 14px;
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 4px;
    line-height: 1.4;
}

.paper-item-meta {
    font-size: 12px;
    color: #6b7280;
}

.already-added {
    color: #10b981;
    font-weight: 500;
    margin-left: 8px;
}

.paper-item.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.paper-item.disabled .paper-checkbox {
    cursor: not-allowed;
}

.preread-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 4px;
    background: #f3f4f6;
    color: #6b7280;
}

.preread-status.has-formal {
    background: #dcfce7;
    color: #166534;
}

.preread-status.has-hidden {
    background: #fef3c7;
    color: #92400e;
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    border-top: 1px solid #e5e7eb;
    background: #f9fafb;
}

.modal-btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.modal-btn.secondary {
    background: #ffffff;
    border-color: #d1d5db;
    color: #374151;
}

.modal-btn.secondary:hover {
    background: #f9fafb;
}

.modal-btn.primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.modal-btn.primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-1px);
}

.modal-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
} 

/* 论文卡片样式 - 所有论文都使用相同的样式 */
.reference-papers-list {
    margin-top: 3px;
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.reference-paper-card {
    background: #ffffff;
    border: 1px solid #f0e6e9;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.2s ease;
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reference-paper-card:hover {
    background: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(209, 119, 143, 0.15);
    border-color: var(--primary-color);
}

.reference-paper-card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0 0 8px 0;
    line-height: 1.4;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
}

.reference-paper-card-meta {
    font-size: 14px;
    color: var(--text-light);
    margin: 0 0 12px 0;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
}

.reference-paper-card-source {
    font-size: 12px;
    color: #999999;
    margin: 0 0 16px 0;
}

.reference-paper-card-preread {
    margin-top: 16px;
}

.reference-paper-card-preread-btn {
    width: 100%;
    background: var(--gradient-1);
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.reference-paper-card-preread-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(209, 119, 143, 0.3);
}

.reference-paper-card-preread-hint {
    font-size: 12px;
    color: var(--text-light);
    margin: 8px 0 0 0;
    text-align: center;
}

.reference-paper-card-remove {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(239, 68, 68, 0.1);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    color: #dc2626;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    opacity: 0;
}

.reference-paper-card:hover .reference-paper-card-remove,
.paper-info:hover .reference-paper-card-remove {
    opacity: 1;
}

.reference-paper-card-remove:hover {
    background: rgba(239, 68, 68, 0.2);
    transform: scale(1.1);
}

/* 置顶按钮样式 */
.reference-paper-card-pin {
    position: absolute;
    top: 8px;
    right: 40px;
    width: 24px;
    height: 24px;
    border: none;
    background: #f3f4f6;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #6b7280;
    transition: all 0.2s ease;
    opacity: 0.7;
}

.reference-paper-card-pin:hover {
    background: #e5e7eb;
    color: #374151;
    opacity: 1;
    transform: scale(1.1);
}

.reference-paper-card-pin.pinned {
    background: #d1778f;
    color: white;
    opacity: 1;
}

.reference-paper-card-pin.pinned:hover {
    background: #b96a7e;
    transform: scale(1.1);
}

/* 当前论文样式 */
.info-title.current-paper {
    color: #d1778f;
    font-weight: 600;
}

.paper-info.current-paper {
    border: 2px solid #d1778f;
    background: linear-gradient(135deg, #fdf8f9 0%, #f8f3f4 100%);
}

/* 通知消息样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideInRight 0.3s ease;
    max-width: 300px;
    word-wrap: break-word;
}

.notification.success {
    background: #10b981;
}

.notification.error {
    background: #dc2626;
}

.notification.info {
    background: #3b82f6;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
} 

/* --- AI Source Highlighting --- */

.ai-highlight {
  background-color: #FFD700; /* Gold */
  opacity: 0.4;
  position: absolute;
  z-index: 20; /* Ensure it's above the canvas but below UI elements */
  pointer-events: none; /* Allows mouse events to pass through to the canvas */
  border-radius: 2px;
}

.user-highlight {
  background-color: #ADD8E6; /* Light Blue */
  opacity: 0.5;
  position: absolute;
  z-index: 20;
  pointer-events: none;
  border-radius: 2px;
} 

/* 错误动画 */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* 输入框动画优化 */
.input-container {
    transition: all 0.3s ease;
    border-radius: 12px;
    background: #ffffff;
    border: 2px solid #f0e6e9;
}

.input-container:focus-within {
    border-color: var(--success-color);
    box-shadow: 0 4px 20px rgba(123, 179, 217, 0.2);
    transform: scale(1.02);
}

.message-input {
    transition: all 0.3s ease;
    border: none;
    outline: none;
    resize: none;
    min-height: 44px;
    max-height: 120px;
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.5;
    background: transparent;
}

/* 发送按钮动画 */
.send-button {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.send-button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.send-button:hover::before {
    width: 40px;
    height: 40px;
}

.send-button:active {
    transform: scale(0.95);
}

/* 消息完成动画 */
.message.completed {
    animation: messageComplete 0.3s ease-out;
}

@keyframes messageComplete {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* 加载状态动画 */
.message.assistant.loading {
    position: relative;
}

.message.assistant.loading::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--success-color), var(--primary-color));
    animation: loadingProgress 2s ease-in-out infinite;
}

@keyframes loadingProgress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* 通知动画优化 */
.notification {
    animation: slideInRight 0.4s ease-out;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    padding: 12px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    opacity: 0;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.notification.error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.notification.info {
    background: linear-gradient(135deg, var(--success-color), #6ba5d1);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .streaming-content {
        font-size: 14px;
        line-height: 1.5;
    }
    
    .streaming-content pre {
        padding: 16px;
        font-size: 12px;
        border-radius: 8px;
    }
    
    .streaming-content code {
        font-size: 12px;
        padding: 2px 6px;
    }
    
    .input-container {
        border-radius: 8px;
    }
    
    .message-input {
        padding: 10px 14px;
        font-size: 13px;
    }
    
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }
    
    .notification.show {
        transform: translateY(0);
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .streaming-content {
        color: #e2e8f0;
    }
    
    .streaming-content pre {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .streaming-content code {
        background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
        color: #f7fafc;
        border-color: #718096;
    }
    
    .streaming-content strong {
        color: #f7fafc;
    }
    
    .streaming-content em {
        color: #cbd5e0;
        background: linear-gradient(135deg, rgba(123, 179, 217, 0.2) 0%, transparent 100%);
    }
    
    .streaming-content blockquote {
        background: linear-gradient(135deg, rgba(123, 179, 217, 0.1) 0%, rgba(123, 179, 217, 0.05) 100%);
        border-left-color: var(--success-color);
        color: #cbd5e0;
    }
}

/* 性能优化 */
.streaming-content * {
    will-change: auto;
}

.streaming-content .typing-text {
    will-change: contents;
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
    .streaming-content,
    .streaming-content *,
    .message,
    .input-container,
    .send-button {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .streaming-content::after,
    .streaming-content .typing-text::after {
        animation: none !important;
    }
} 

/* 论文选择功能样式 */
.paper-selection-section {
    margin: 20px 0;
    padding: 16px;
    background: #ffffff;
    border-radius: 12px;
    border: 1px solid #f0e6e9;
}

.paper-select-button {
    width: 100%;
    padding: 12px 16px;
    background: linear-gradient(135deg, #7bb3d9 0%, #6ba5d1 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.paper-select-button:hover {
    background: linear-gradient(135deg, #6ba5d1 0%, #5a97c9 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(123, 179, 217, 0.3);
}

.select-icon {
    font-size: 16px;
}

.select-desc {
    font-size: 12px;
    color: #666666;
    text-align: center;
    margin-top: 8px;
}

/* 模态框样式增强 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: white;
    border-radius: 16px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #f0e6e9;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
    margin: 0;
}

.close-button {
    background: none;
    border: none;
    font-size: 24px;
    color: #666666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-button:hover {
    background: #f5f5f5;
    color: #333333;
}

.modal-body {
    padding: 24px;
    max-height: 400px;
    overflow-y: auto;
}

.search-container {
    margin-bottom: 16px;
}

.search-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.search-input:focus {
    outline: none;
    border-color: #7bb3d9;
    box-shadow: 0 0 0 3px rgba(123, 179, 217, 0.1);
}

.paper-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.paper-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border: 1px solid #f0e6e9;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.paper-item:hover {
    border-color: #7bb3d9;
    background: #f8fbff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(123, 179, 217, 0.1);
}

.paper-item.current-paper {
    border-color: #7bb3d9;
    background: #e6f3ff;
}

.paper-item-content {
    flex: 1;
    margin-right: 12px;
}

.paper-item-title {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 4px;
    line-height: 1.4;
}

.paper-item-authors {
    font-size: 12px;
    color: #666666;
    margin-bottom: 2px;
}

.paper-item-source {
    font-size: 11px;
    color: #999999;
}

.paper-item-checkbox {
    display: flex;
    align-items: center;
}

.paper-item-checkbox input[type="radio"] {
    width: 16px;
    height: 16px;
    accent-color: #7bb3d9;
}

.modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #f0e6e9;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.cancel-button {
    padding: 10px 20px;
    background: #f5f5f5;
    color: #666666;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.cancel-button:hover {
    background: #e5e7eb;
    color: #333333;
}

.confirm-button {
    padding: 10px 20px;
    background: linear-gradient(135deg, #7bb3d9 0%, #6ba5d1 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.confirm-button:hover {
    background: linear-gradient(135deg, #6ba5d1 0%, #5a97c9 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(123, 179, 217, 0.3);
}

/* 消息动画样式 */
.message {
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        max-height: 90vh;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 16px;
    }
    
    .paper-item {
        padding: 10px 12px;
    }
    
    .paper-item-title {
        font-size: 13px;
    }
    
    .paper-item-authors {
        font-size: 11px;
    }
    
    .paper-item-source {
        font-size: 10px;
    }
}