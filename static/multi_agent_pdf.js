// 多Agent页面PDF显示逻辑 - 与chat.js完全一致
document.addEventListener('DOMContentLoaded', function() {
    console.log('Multi-agent PDF script loaded');

    // 检查必要的DOM元素
    const viewer = document.getElementById('pdf-viewer');
    if (!viewer) {
        console.error('PDF viewer element not found');
        return;
    }

    // --- PDF渲染逻辑 ---
    let pdfDoc = null;
    let pageNum = 1;
    let pageRendering = false;
    let pageNumPending = null;
    let scaleMultiplier = 1.0;

    function renderPage(num) {
        pageRendering = true;
        
        viewer.innerHTML = '';
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const textLayer = document.createElement('div');
        textLayer.className = 'textLayer';
        viewer.appendChild(canvas);
        viewer.appendChild(textLayer);
        
        pdfDoc.getPage(num).then(function(page) {
            const container = document.getElementById('pdf-viewer');
            if (!container) return;
            const containerWidth = container.clientWidth;
            const containerHeight = container.clientHeight;

            const unscaledViewport = page.getViewport({ scale: 1 });
            
            const scaleX = containerWidth / (unscaledViewport.width + 2);
            const scaleY = containerHeight / (unscaledViewport.height + 2);
            const baseScale = Math.min(scaleX, scaleY);
            
            const scale = baseScale * scaleMultiplier;
            const viewport = page.getViewport({ scale: scale });

            const devicePixelRatio = window.devicePixelRatio || 1;

            canvas.width = Math.floor(viewport.width * devicePixelRatio);
            canvas.height = Math.floor(viewport.height * devicePixelRatio);

            canvas.style.width = `${Math.floor(viewport.width)}px`;
            canvas.style.height = `${Math.floor(viewport.height)}px`;
            
            textLayer.style.width = canvas.style.width;
            textLayer.style.height = canvas.style.height;
            textLayer.style.left = '0px';
            textLayer.style.top = '0px';
            
            ctx.setTransform(devicePixelRatio, 0, 0, devicePixelRatio, 0, 0);

            const renderContext = {
                canvasContext: ctx,
                viewport: viewport
            };
            
            const renderTask = page.render(renderContext);

            page.getTextContent().then(function(textContent) {
                pdfjsLib.renderTextLayer({
                    textContent: textContent,
                    container: textLayer,
                    viewport: viewport,
                    textDivs: []
                });
            });

            renderTask.promise.then(function() {
                pageRendering = false;
                if (pageNumPending !== null) {
                    renderPage(pageNumPending);
                    pageNumPending = null;
                }
            });
        });

        document.getElementById('page_num').textContent = num;
        document.getElementById('zoomLevel').textContent = `${Math.round(scaleMultiplier * 100)}%`;
    }
    
    function queueRenderPage(num) {
        if (pageRendering) {
            pageNumPending = num;
        } else {
            renderPage(num);
        }
    }

    function onPrevPage() {
        if (pageNum <= 1) return;
        pageNum--;
        queueRenderPage(pageNum);
    }

    function onNextPage() {
        if (pageNum >= pdfDoc.numPages) return;
        pageNum++;
        queueRenderPage(pageNum);
    }

    // 绑定PDF控制按钮
    document.getElementById('prevPageBtn').addEventListener('click', onPrevPage);
    document.getElementById('nextPageBtn').addEventListener('click', onNextPage);

    document.getElementById('zoomInBtn').addEventListener('click', function() {
        if (scaleMultiplier >= 3.0) return;
        scaleMultiplier += 0.2;
        queueRenderPage(pageNum);
    });

    document.getElementById('zoomOutBtn').addEventListener('click', function() {
        if (scaleMultiplier <= 0.4) return;
        scaleMultiplier -= 0.2;
        queueRenderPage(pageNum);
    });
    
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            if (pdfDoc) {
                queueRenderPage(pageNum);
            }
        }, 150);
    });
    
    function initializePdfViewer(url) {
        console.log('Initializing PDF viewer with URL:', url);

        if (typeof pdfjsLib === 'undefined') {
            console.error("PDF.js is not loaded.");
            viewer.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">PDF.js库未加载</div>';
            return;
        }

        pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.11.338/pdf.worker.min.js`;

        viewer.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">正在加载PDF...</div>';

        pdfjsLib.getDocument(url).promise.then(function(pdfDoc_) {
            console.log('PDF loaded successfully, pages:', pdfDoc_.numPages);
            pdfDoc = pdfDoc_;
            const pageCountElement = document.getElementById('page_count');
            if (pageCountElement) {
                pageCountElement.textContent = pdfDoc.numPages;
            }
            pageNum = 1; // Reset to first page
            renderPage(pageNum);
        }).catch(function(reason) {
            console.error('Error during PDF loading:', reason);
            viewer.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">PDF加载失败: ' + reason + '</div>';
        });
    }

    // 更新PDF显示
    function updatePdfDisplay(paperId) {
        const newPdfUrl = `/view_pdf/${paperId}`;
        initializePdfViewer(newPdfUrl);
    }

    // 将updatePdfDisplay暴露到全局，以便其他地方调用
    window.updatePdfDisplay = updatePdfDisplay;

    // Initial PDF load - 使用全局变量
    const pdfUrlToUse = (typeof pdfUrl !== 'undefined' && pdfUrl) ? pdfUrl :
                        (typeof currentPdfUrl !== 'undefined' && currentPdfUrl) ? currentPdfUrl : null;

    console.log('PDF URL to use:', pdfUrlToUse);

    if (pdfUrlToUse) {
        initializePdfViewer(pdfUrlToUse);
    } else {
        console.error('No PDF URL found');
        viewer.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">PDF URL未定义</div>';
    }
});
