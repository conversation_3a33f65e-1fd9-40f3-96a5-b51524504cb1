from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Paper(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(500), nullable=False)
    authors = db.Column(db.String(500), nullable=True)
    abstract = db.Column(db.Text, nullable=True)
    date = db.Column(db.String(20), nullable=True)
    source = db.Column(db.String(100), nullable=True)
    file_path = db.Column(db.String(500), nullable=True)
    arxiv_id = db.Column(db.String(50), nullable=True)
    has_pdf = db.Column(db.Bo<PERSON>, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # New fields for pre-reads
    hidden_pre_read_path = db.Column(db.String(500), nullable=True)
    pre_read_path = db.Column(db.String(500), nullable=True)

    # Relationships
    history = db.relationship('PaperHistory', uselist=False, back_populates='paper', cascade="all, delete-orphan")
    citations = db.relationship('Citation', backref='paper', lazy=True, cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'authors': self.authors,
            'abstract': self.abstract,
            'date': self.date,
            'source': self.source,
            'file_path': self.file_path,
            'arxiv_id': self.arxiv_id,
            'has_pdf': self.has_pdf,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'hidden_pre_read_path': self.hidden_pre_read_path,
            'pre_read_path': self.pre_read_path,
            'history': self.history.to_dict() if self.history else None
        }

class PaperHistory(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    paper_id = db.Column(db.Integer, db.ForeignKey('paper.id'), nullable=False, unique=True)
    paper = db.relationship('Paper', back_populates='history')
    dialogue = db.relationship('DialogueMessage', backref='history', lazy=True, cascade="all, delete-orphan")

    def to_dict(self):
        return {
            'id': self.id,
            'paper_id': self.paper_id,
            'dialogue': [message.to_dict() for message in self.dialogue]
        }

class DialogueMessage(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    history_id = db.Column(db.Integer, db.ForeignKey('paper_history.id'), nullable=False)
    role = db.Column(db.String(80), nullable=False) # 'user' or 'assistant'
    content = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'history_id': self.history_id,
            'role': self.role,
            'content': self.content,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }

class Citation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    paper_id = db.Column(db.Integer, db.ForeignKey('paper.id'), nullable=False) # The paper that makes the citation
    title = db.Column(db.String(500), nullable=False) # Title of the cited paper
    authors = db.Column(db.String(500), nullable=True) # Authors of the cited paper

    def to_dict(self):
        return {
            'id': self.id,
            'paper_id': self.paper_id,
            'title': self.title,
            'authors': self.authors
        } 