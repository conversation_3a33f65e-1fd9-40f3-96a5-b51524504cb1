[{"title": "Attention Is All You Need", "authors": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>", "abstract": "The dominant sequence transduction models are based on complex recurrent or\nconvolutional neural networks in an encoder-decoder configuration. The best\nperforming models also connect the encoder and decoder through an attention\nmechanism. We propose a new simple network architecture, the Transformer, based\nsolely on attention mechanisms, dispensing with recurrence and convolutions\nentirely. Experiments on two machine translation tasks show these models to be\nsuperior in quality while being more parallelizable and requiring significantly\nless time to train. Our model achieves 28.4 BLEU on the WMT 2014\nEnglish-to-German translation task, improving over the existing best results,\nincluding ensembles by over 2 BLEU. On the WMT 2014 English-to-French\ntranslation task, our model establishes a new single-model state-of-the-art\nBLEU score of 41.8 after training for 3.5 days on eight GPUs, a small fraction\nof the training costs of the best models from the literature. We show that the\nTransformer generalizes well to other tasks by applying it successfully to\nEnglish constituency parsing both with large and limited training data.", "date": "2017-06-12", "source": "arXiv:1706.03762", "arxiv_id": "1706.03762", "file_path": "uploads/arxiv_1706.03762_20250723_222725.pdf", "has_pdf": true, "id": 2}, {"title": "RAD: Region-Aware Diffusion Models for Image Inpainting", "authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "abstract": "Diffusion models have achieved remarkable success in im-\nage generation, with applications broadening across vari-\nous domains. Inpainting is one such application that can\nbenefit significantly from diffusion models. Existing meth-\nods either hijack the reverse process of a pretrained diffu-\nsion model or cast the problem into a larger framework,\ni.e., conditioned generation. However, these approaches of-\nten require nested loops in the generation process or ad-\nditional components for condition", "date": "2025-07-24", "source": "PDF Upload", "has_pdf": true, "id": 3, "file_path": "uploads/20250724_005400_Kim_RAD_Region-Aware_Diffusion_Models_for_Image_Inpainting_CVPR_2025_paper.pdf"}]