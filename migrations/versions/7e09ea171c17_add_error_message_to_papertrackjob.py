"""Add error_message to PaperTrackJob

Revision ID: 7e09ea171c17
Revises: 5098197a9068
Create Date: 2025-07-24 09:34:26.462111

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7e09ea171c17'
down_revision = '5098197a9068'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('paper_track_job', sa.Column('error_message', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('paper_track_job', 'error_message')
    # ### end Alembic commands ###
