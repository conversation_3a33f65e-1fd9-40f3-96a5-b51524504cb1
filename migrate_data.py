import json
import os
from app import app, db, Paper

def migrate_data():
    """
    Migrates paper data from the old JSON file to the new database structure.
    """
    json_file_path = 'data/papers.json'
    if not os.path.exists(json_file_path):
        print("papers.json not found, no data to migrate.")
        return

    with open(json_file_path, 'r', encoding='utf-8') as f:
        try:
            papers_data = json.load(f)
        except json.JSONDecodeError:
            print("Could not decode JSON from papers.json.")
            return

    with app.app_context():
        existing_papers = {p.id for p in Paper.query.all()}
        migrated_count = 0
        for paper_dict in papers_data:
            if paper_dict.get('id') in existing_papers:
                print(f"Skipping paper with ID {paper_dict.get('id')} as it already exists.")
                continue

            new_paper = Paper(
                id=paper_dict.get('id'),
                title=paper_dict.get('title'),
                authors=paper_dict.get('authors'),
                abstract=paper_dict.get('abstract'),
                date=paper_dict.get('date'),
                source=paper_dict.get('source'),
                file_path=paper_dict.get('file_path'),
                arxiv_id=paper_dict.get('arxiv_id'),
                has_pdf=paper_dict.get('has_pdf', False)
            )
            db.session.add(new_paper)
            migrated_count += 1

        if migrated_count > 0:
            db.session.commit()
            print(f"Successfully migrated {migrated_count} papers to the database.")
        else:
            print("No new papers to migrate.")

if __name__ == '__main__':
    migrate_data()
    # Optionally, rename the old file to prevent re-running the migration
    json_file_path = 'data/papers.json'
    if os.path.exists(json_file_path):
        os.rename(json_file_path, 'data/papers.json.migrated')
        print(f"Renamed {json_file_path} to data/papers.json.migrated") 